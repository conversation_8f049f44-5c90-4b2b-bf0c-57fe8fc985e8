import { create } from 'zustand'
import { api } from '@/lib/api'

export interface User {
  id: string
  email: string
  username: string
  full_name: string
  is_active: boolean
  is_admin: boolean
}

interface AuthState {
  user: User | null
  token: string | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (email: string, password: string) => Promise<void>
  logout: () => void
  initializeAuth: () => void
  setLoading: (loading: boolean) => void
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  token: null,
  isLoading: false,
  isAuthenticated: false,

  login: async (email: string, password: string) => {
    set({ isLoading: true })
    try {
      const response = await api.post('/auth/login', { email, password })
      const { access_token, user } = response.data

      set({
        token: access_token,
        user,
        isAuthenticated: true,
        isLoading: false,
      })

      // Set token for future requests
      api.defaults.headers.common['Authorization'] = `Bearer ${access_token}`

      // Store in localStorage
      localStorage.setItem('auth-token', access_token)
      localStorage.setItem('auth-user', JSON.stringify(user))
    } catch (error) {
      set({ isLoading: false })
      throw error
    }
  },

  logout: () => {
    set({
      user: null,
      token: null,
      isAuthenticated: false,
    })

    // Remove token from API headers
    delete api.defaults.headers.common['Authorization']

    // Clear localStorage
    localStorage.removeItem('auth-token')
    localStorage.removeItem('auth-user')
  },

  initializeAuth: async () => {
    try {
      const token = localStorage.getItem('auth-token')
      const userStr = localStorage.getItem('auth-user')

      if (token && userStr) {
        const user = JSON.parse(userStr)

        // Set token for API requests
        api.defaults.headers.common['Authorization'] = `Bearer ${token}`

        // Verify token is still valid
        const response = await api.get('/auth/me')
        set({
          token,
          user: response.data,
          isAuthenticated: true,
        })
      }
    } catch (error) {
      // Token is invalid, logout
      get().logout()
    }
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading })
  },
}))
