import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { api } from '@/lib/api'

export interface User {
  id: string
  email: string
  username: string
  full_name: string
  is_active: boolean
  is_admin: boolean
}

interface AuthState {
  user: User | null
  token: string | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (email: string, password: string) => Promise<void>
  logout: () => void
  initializeAuth: () => void
  setLoading: (loading: boolean) => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isLoading: false,
      isAuthenticated: false,

      login: async (email: string, password: string) => {
        set({ isLoading: true })
        try {
          const response = await api.post('/auth/login', { email, password })
          const { access_token, user } = response.data
          
          set({
            token: access_token,
            user,
            isAuthenticated: true,
            isLoading: false,
          })

          // Set token for future requests
          api.defaults.headers.common['Authorization'] = `Bearer ${access_token}`
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
        })
        
        // Remove token from API headers
        delete api.defaults.headers.common['Authorization']
        
        // Clear localStorage
        localStorage.removeItem('auth-storage')
      },

      initializeAuth: async () => {
        const state = get()
        if (state.token) {
          try {
            // Set token for API requests
            api.defaults.headers.common['Authorization'] = `Bearer ${state.token}`
            
            // Verify token is still valid
            const response = await api.get('/auth/me')
            set({
              user: response.data,
              isAuthenticated: true,
            })
          } catch (error) {
            // Token is invalid, logout
            get().logout()
          }
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        token: state.token,
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)
