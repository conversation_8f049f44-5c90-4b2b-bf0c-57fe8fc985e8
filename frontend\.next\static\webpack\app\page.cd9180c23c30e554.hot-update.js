"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/stores/auth-store.ts":
/*!**********************************!*\
  !*** ./lib/stores/auth-store.ts ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: function() { return /* binding */ useAuthStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)((set, get)=>({\n        user: null,\n        token: null,\n        isLoading: false,\n        isAuthenticated: false,\n        login: async (email, password)=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.api.post(\"/auth/login\", {\n                    email,\n                    password\n                });\n                const { access_token, user } = response.data;\n                set({\n                    token: access_token,\n                    user,\n                    isAuthenticated: true,\n                    isLoading: false\n                });\n                // Set token for future requests\n                _lib_api__WEBPACK_IMPORTED_MODULE_0__.api.defaults.headers.common[\"Authorization\"] = \"Bearer \".concat(access_token);\n                // Store in localStorage\n                localStorage.setItem(\"auth-token\", access_token);\n                localStorage.setItem(\"auth-user\", JSON.stringify(user));\n            } catch (error) {\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        logout: ()=>{\n            set({\n                user: null,\n                token: null,\n                isAuthenticated: false\n            });\n            // Remove token from API headers\n            delete _lib_api__WEBPACK_IMPORTED_MODULE_0__.api.defaults.headers.common[\"Authorization\"];\n            // Clear localStorage\n            localStorage.removeItem(\"auth-token\");\n            localStorage.removeItem(\"auth-user\");\n        },\n        initializeAuth: async ()=>{\n            try {\n                const token = localStorage.getItem(\"auth-token\");\n                const userStr = localStorage.getItem(\"auth-user\");\n                if (token && userStr) {\n                    const user = JSON.parse(userStr);\n                    // Set token for API requests\n                    _lib_api__WEBPACK_IMPORTED_MODULE_0__.api.defaults.headers.common[\"Authorization\"] = \"Bearer \".concat(token);\n                    // Verify token is still valid\n                    const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.api.get(\"/auth/me\");\n                    set({\n                        token,\n                        user: response.data,\n                        isAuthenticated: true\n                    });\n                }\n            } catch (error) {\n                // Token is invalid, logout\n                get().logout();\n            }\n        },\n        setLoading: (loading)=>{\n            set({\n                isLoading: loading\n            });\n        }\n    }));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/stores/auth-store.ts\n"));

/***/ })

});