"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stores/auth-store */ \"(app-pages-browser)/./lib/stores/auth-store.ts\");\n/* harmony import */ var _lib_stores_websocket_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/stores/websocket-store */ \"(app-pages-browser)/./lib/stores/websocket-store.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/dashboard-layout */ \"(app-pages-browser)/./components/layout/dashboard-layout.tsx\");\n/* harmony import */ var _components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/chat/chat-interface */ \"(app-pages-browser)/./components/chat/chat-interface.tsx\");\n/* harmony import */ var _components_chat_chat_history__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/chat/chat-history */ \"(app-pages-browser)/./components/chat/chat-history.tsx\");\n/* harmony import */ var _components_files_file_viewer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/files/file-viewer */ \"(app-pages-browser)/./components/files/file-viewer.tsx\");\n/* harmony import */ var _components_chat_new_chat_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/chat/new-chat-dialog */ \"(app-pages-browser)/./components/chat/new-chat-dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Menu,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Menu,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Menu,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Menu,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Menu,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const { user, isAuthenticated } = (0,_lib_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    const { connect } = (0,_lib_stores_websocket_store__WEBPACK_IMPORTED_MODULE_3__.useWebSocketStore)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [selectedChatId, setSelectedChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isHistoryOpen, setIsHistoryOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFilesOpen, setIsFilesOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewChatOpen, setIsNewChatOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isAuthenticated) {\n            router.push(\"/login\");\n            return;\n        }\n        // Connect to WebSocket\n        if (user === null || user === void 0 ? void 0 : user.id) {\n            connect(user.id);\n        }\n    }, [\n        isAuthenticated,\n        user,\n        router,\n        connect\n    ]);\n    const handleNewChat = ()=>{\n        setIsNewChatOpen(true);\n    };\n    const handleChatCreated = (chatId)=>{\n        setSelectedChatId(chatId);\n        setIsNewChatOpen(false);\n    };\n    const handleChatSelect = (chatId)=>{\n        setSelectedChatId(chatId);\n    };\n    if (!isAuthenticated || !user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_5__.DashboardLayout, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-screen relative overflow-hidden\",\n                style: {\n                    background: \"var(--gradient-dark)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"floating-orb\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"floating-orb\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"floating-orb\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sparkle\",\n                                style: {\n                                    top: \"15%\",\n                                    left: \"5%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sparkle\",\n                                style: {\n                                    top: \"70%\",\n                                    right: \"10%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sparkle\",\n                                style: {\n                                    bottom: \"25%\",\n                                    left: \"15%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sparkle\",\n                                style: {\n                                    top: \"35%\",\n                                    right: \"25%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"relative z-10 transition-all duration-500 ease-in-out\", isHistoryOpen ? \"w-80\" : \"w-0\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"h-full flex flex-col glass-card border-r border-white/10 rounded-none\", !isHistoryOpen && \"hidden\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-8 border-b border-white/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"font-bold text-3xl text-white\",\n                                                    children: \"Chat History\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsHistoryOpen(false),\n                                                    className: \"p-2 rounded-xl hover:bg-white/10 text-white/70 hover:text-white transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-6 w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNewChat,\n                                            className: \"glass-button w-full h-14 text-lg font-bold\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"New Research\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_history__WEBPACK_IMPORTED_MODULE_7__.ChatHistory, {\n                                        selectedChatId: selectedChatId,\n                                        onChatSelect: handleChatSelect\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-20 backdrop-blur-xl bg-white/5 border-b border-white/10 px-8 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4\",\n                                        children: [\n                                            !isHistoryOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>setIsHistoryOpen(true),\n                                                className: \"hover:bg-white/10 text-white p-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-gradient-to-br from-blue-500 via-purple-600 to-pink-600 rounded-2xl flex items-center justify-center shadow-2xl\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 124,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 123,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 126,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"font-black text-2xl text-white\",\n                                                                children: selectedChatId ? \"Research Session\" : \"Gemini CrewAI\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 129,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-slate-300 text-sm\",\n                                                                children: \"Next-Generation AI Platform\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 132,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>setIsFilesOpen(!isFilesOpen),\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"hover:bg-white/10 text-white p-3 transition-all duration-200\", isFilesOpen && \"bg-white/20 text-blue-300\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_6__.ChatInterface, {\n                                    chatId: selectedChatId,\n                                    onNewChat: handleNewChat\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"relative z-10 transition-all duration-500 ease-in-out\", isFilesOpen ? \"w-96\" : \"w-0\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"h-full flex flex-col backdrop-blur-xl bg-white/5 border-l border-white/10\", !isFilesOpen && \"hidden\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-white/10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"font-bold text-2xl text-white\",\n                                                children: \"Generated Files\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>setIsFilesOpen(false),\n                                                className: \"hover:bg-white/10 text-white\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_files_file_viewer__WEBPACK_IMPORTED_MODULE_8__.FileViewer, {\n                                        chatId: selectedChatId\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_new_chat_dialog__WEBPACK_IMPORTED_MODULE_9__.NewChatDialog, {\n                open: isNewChatOpen,\n                onOpenChange: setIsNewChatOpen,\n                onChatCreated: handleChatCreated\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"PUnEbJnxfipzKwnvNpDngyeCAtc=\", false, function() {\n    return [\n        _lib_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__.useAuthStore,\n        _lib_stores_websocket_store__WEBPACK_IMPORTED_MODULE_3__.useWebSocketStore,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ })

});