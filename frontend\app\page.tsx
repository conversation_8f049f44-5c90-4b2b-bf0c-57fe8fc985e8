'use client'

import Link from 'next/link'
import { Bot, Sparkles, Search, FileText, BarChart3, Users, ArrowRight, Play, Star, Zap } from 'lucide-react'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-[#0a0a0a] text-white">
      <div className="animated-bg"></div>

      {/* Navigation */}
      <nav className="relative z-50 glass-dark border-b border-white/10">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25 pulse-glow">
                <Bot className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold gradient-text">CrewAI</span>
            </div>

            <div className="hidden md:flex items-center gap-8">
              <a href="#features" className="text-gray-300 hover:text-white transition-colors">Features</a>
              <a href="#how-it-works" className="text-gray-300 hover:text-white transition-colors">How it Works</a>
              <a href="#pricing" className="text-gray-300 hover:text-white transition-colors">Pricing</a>
            </div>

            <Link href="/login" className="btn-primary">
              Sign In
            </Link>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative z-10 pt-20 pb-32">
        <div className="max-w-7xl mx-auto px-6 text-center">
          <div className="mb-8">
            <div className="inline-flex items-center gap-2 bg-blue-500/10 border border-blue-500/20 rounded-full px-4 py-2 mb-8">
              <Sparkles className="h-4 w-4 text-blue-400" />
              <span className="text-sm text-blue-300">Next-Generation AI Research Platform</span>
            </div>
          </div>

          <h1 className="text-6xl md:text-8xl font-black mb-8 leading-tight">
            <span className="gradient-text">Intelligent Research</span>
            <br />
            <span className="text-white">Powered by AI Agents</span>
          </h1>

          <p className="text-xl md:text-2xl text-gray-400 max-w-4xl mx-auto mb-12 leading-relaxed">
            Transform your research workflow with collaborative AI agents that analyze, synthesize, and deliver comprehensive insights in minutes, not hours.
          </p>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-16">
            <Link href="/login" className="btn-primary text-lg px-8 py-4 group">
              <div className="flex items-center gap-3">
                <Play className="h-5 w-5" />
                <span>Start Research</span>
                <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </div>
            </Link>

            <button type="button" className="card px-8 py-4 text-lg group">
              <div className="flex items-center gap-3">
                <Zap className="h-5 w-5 text-yellow-400" />
                <span>Watch Demo</span>
              </div>
            </button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="card text-center">
              <div className="text-3xl font-bold gradient-text mb-2">10x</div>
              <div className="text-gray-400">Faster Research</div>
            </div>
            <div className="card text-center">
              <div className="text-3xl font-bold gradient-text mb-2">95%</div>
              <div className="text-gray-400">Accuracy Rate</div>
            </div>
            <div className="card text-center">
              <div className="text-3xl font-bold gradient-text mb-2">24/7</div>
              <div className="text-gray-400">AI Availability</div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="relative z-10 py-32">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-6xl font-bold mb-6">
              <span className="gradient-text">Powerful Features</span>
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Everything you need to conduct comprehensive research with AI-powered agents
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="card group">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform shadow-lg shadow-blue-500/25 floating">
                <Search className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-bold mb-3 gradient-text">Intelligent Search</h3>
              <p className="text-gray-400">Advanced AI agents that understand context and deliver precise research results from multiple sources.</p>
            </div>

            <div className="card group">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform shadow-lg shadow-purple-500/25 floating-delayed">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-bold mb-3 gradient-text">Document Analysis</h3>
              <p className="text-gray-400">Upload and analyze documents with AI that extracts key insights and generates comprehensive summaries.</p>
            </div>

            <div className="card group">
              <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform shadow-lg shadow-emerald-500/25 floating">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-bold mb-3 gradient-text">Data Visualization</h3>
              <p className="text-gray-400">Transform complex data into beautiful, interactive visualizations and reports.</p>
            </div>

            <div className="card group">
              <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform shadow-lg shadow-orange-500/25 floating-delayed">
                <Users className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-bold mb-3 gradient-text">Collaborative Agents</h3>
              <p className="text-gray-400">Multiple AI agents work together to provide comprehensive research from different perspectives.</p>
            </div>

            <div className="card group">
              <div className="w-12 h-12 bg-gradient-to-br from-pink-500 to-pink-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform shadow-lg shadow-pink-500/25 floating">
                <Sparkles className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-bold mb-3 gradient-text">Real-time Insights</h3>
              <p className="text-gray-400">Get instant insights and recommendations as your research progresses.</p>
            </div>

            <div className="card group">
              <div className="w-12 h-12 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform shadow-lg shadow-cyan-500/25 floating-delayed">
                <Bot className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-bold mb-3 gradient-text">AI Automation</h3>
              <p className="text-gray-400">Automate repetitive research tasks and focus on analysis and decision-making.</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 py-32">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-4xl md:text-6xl font-bold mb-8">
            <span className="gradient-text">Ready to Transform</span>
            <br />
            <span className="text-white">Your Research?</span>
          </h2>
          <p className="text-xl text-gray-400 mb-12 max-w-2xl mx-auto">
            Join thousands of researchers who are already using CrewAI to accelerate their work and discover new insights.
          </p>

          <Link href="/login" className="btn-primary text-xl px-12 py-6 group">
            <div className="flex items-center gap-4">
              <Star className="h-6 w-6 group-hover:animate-pulse floating" />
              <span className="gradient-text">Get Started Today</span>
              <ArrowRight className="h-6 w-6 group-hover:translate-x-2 transition-transform floating-delayed" />
            </div>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative z-10 border-t border-white/10 py-12">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center gap-3 mb-4 md:mb-0">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Bot className="h-4 w-4 text-white" />
              </div>
              <span className="text-lg font-bold gradient-text">CrewAI</span>
            </div>

            <div className="text-gray-400 text-sm">
              © 2024 CrewAI. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
