import { create } from 'zustand'

export interface WebSocketMessage {
  type: string
  chat_id?: string
  agent_name?: string
  message?: string
  message_type?: string
  metadata?: any
  progress?: number
  status?: string
  current_agent?: string
  file_info?: any
  success?: boolean
  final_output?: string
  error?: string
  timestamp: string
}

interface WebSocketState {
  socket: WebSocket | null
  isConnected: boolean
  messages: WebSocketMessage[]
  currentProgress: number
  currentStatus: string
  currentAgent: string | null
  connect: (userId: string) => void
  disconnect: () => void
  sendMessage: (message: any) => void
  addMessage: (message: WebSocketMessage) => void
  clearMessages: () => void
}

export const useWebSocketStore = create<WebSocketState>((set, get) => ({
  socket: null,
  isConnected: false,
  messages: [],
  currentProgress: 0,
  currentStatus: 'idle',
  currentAgent: null,

  connect: (userId: string) => {
    const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000'
    const socket = new WebSocket(`${wsUrl}/ws/${userId}`)

    socket.onopen = () => {
      console.log('WebSocket connected')
      set({ isConnected: true })
      
      // Send ping to keep connection alive
      const pingInterval = setInterval(() => {
        if (socket.readyState === WebSocket.OPEN) {
          socket.send(JSON.stringify({ type: 'ping' }))
        } else {
          clearInterval(pingInterval)
        }
      }, 30000) // Ping every 30 seconds
    }

    socket.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data)
        
        // Handle different message types
        switch (message.type) {
          case 'agent_update':
            set((state) => ({
              messages: [...state.messages, message],
            }))
            break
            
          case 'progress_update':
            set({
              currentProgress: message.progress || 0,
              currentStatus: message.status || 'idle',
              currentAgent: message.current_agent || null,
            })
            break
            
          case 'file_update':
            set((state) => ({
              messages: [...state.messages, message],
            }))
            break
            
          case 'completion_update':
            set({
              currentProgress: message.success ? 100 : 0,
              currentStatus: message.success ? 'completed' : 'failed',
              currentAgent: null,
            })
            set((state) => ({
              messages: [...state.messages, message],
            }))
            break
            
          case 'pong':
            // Handle pong response
            break
            
          default:
            console.log('Unknown message type:', message.type)
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error)
      }
    }

    socket.onclose = () => {
      console.log('WebSocket disconnected')
      set({ isConnected: false, socket: null })
    }

    socket.onerror = (error) => {
      console.error('WebSocket error:', error)
      set({ isConnected: false })
    }

    set({ socket })
  },

  disconnect: () => {
    const { socket } = get()
    if (socket) {
      socket.close()
      set({ socket: null, isConnected: false })
    }
  },

  sendMessage: (message: any) => {
    const { socket, isConnected } = get()
    if (socket && isConnected) {
      socket.send(JSON.stringify(message))
    }
  },

  addMessage: (message: WebSocketMessage) => {
    set((state) => ({
      messages: [...state.messages, message],
    }))
  },

  clearMessages: () => {
    set({ 
      messages: [],
      currentProgress: 0,
      currentStatus: 'idle',
      currentAgent: null,
    })
  },
}))
