#!/usr/bin/env python3
"""
Test login functionality
"""

import requests
import json

def test_login():
    """Test the login endpoint"""
    url = "http://localhost:8000/api/auth/login"
    
    # Test data
    login_data = {
        "email": "<EMAIL>",
        "password": "admin123"
    }
    
    try:
        print("Testing login endpoint...")
        print(f"URL: {url}")
        print(f"Data: {login_data}")
        
        response = requests.post(url, json=login_data)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Login successful!")
            print(f"Token: {data.get('access_token', 'N/A')[:50]}...")
            print(f"User: {data.get('user', {}).get('email', 'N/A')}")
            return True
        else:
            print("❌ Login failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error testing login: {e}")
        return False

if __name__ == "__main__":
    print("Testing Gemini CrewAI Login")
    print("=" * 30)
    
    success = test_login()
    
    if success:
        print("\n✅ Login test passed!")
        print("You can now use the web interface at http://localhost:3000")
    else:
        print("\n❌ Login test failed!")
        print("Check if the backend server is running and the database is accessible")
