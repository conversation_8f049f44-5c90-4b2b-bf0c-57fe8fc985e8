# Gemini CrewAI - Modern UI Setup Guide

This guide will help you set up the modern UI for your CrewAI application with authentication, real-time updates, and file management.

## 🏗️ Architecture Overview

- **Backend**: FastAPI with Python (integrates with existing CrewAI)
- **Frontend**: Next.js 14 with TypeScript, Tailwind CSS, and Shadcn/ui
- **Database**: Neon PostgreSQL
- **Authentication**: JWT-based with admin-managed users
- **Real-time**: WebSocket for live agent updates
- **File Management**: Integrated file viewer for outputs

## 📋 Prerequisites

1. **Python 3.10+** (for backend)
2. **Node.js 18+** (for frontend)
3. **Neon Database Account** (for PostgreSQL)
4. **API Keys** (OpenAI, Serper, etc.)

## 🚀 Quick Setup

### 1. Database Setup (Neon)

1. Go to [Neon Console](https://console.neon.tech/)
2. Create a new project
3. Copy your connection string (it looks like: `********************************************`)

### 2. Backend Setup

```bash
# Navigate to backend directory
cd backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Create environment file
cp .env.example .env
```

Edit the `.env` file with your actual values:
```env
DATABASE_URL=postgresql://your-neon-connection-string
SECRET_KEY=your-super-secret-key-change-this
OPENAI_API_KEY=your-openai-api-key
SERPER_API_KEY=your-serper-api-key
ENVIRONMENT=development
```

### 3. Frontend Setup

```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Create environment file
echo "NEXT_PUBLIC_API_URL=http://localhost:8000" > .env.local
echo "NEXT_PUBLIC_WS_URL=ws://localhost:8000" >> .env.local
```

### 4. Create Admin User

Create a script to add the first admin user:

```python
# backend/create_admin.py
import sys
import os
sys.path.append(os.path.dirname(__file__))

from sqlalchemy.orm import Session
from database import SessionLocal, engine
from models.models import Base, User
from services.auth_service import get_password_hash
import uuid

# Create tables
Base.metadata.create_all(bind=engine)

# Create admin user
db = SessionLocal()
try:
    admin_user = User(
        id=str(uuid.uuid4()),
        email="<EMAIL>",
        username="admin",
        hashed_password=get_password_hash("admin123"),
        full_name="System Administrator",
        is_admin=True,
        is_active=True
    )
    db.add(admin_user)
    db.commit()
    print("Admin user created successfully!")
    print("Email: <EMAIL>")
    print("Password: admin123")
    print("Please change the password after first login!")
except Exception as e:
    print(f"Error creating admin user: {e}")
finally:
    db.close()
```

Run the script:
```bash
cd backend
python create_admin.py
```

## 🏃‍♂️ Running the Application

### Start Backend
```bash
cd backend
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### Start Frontend
```bash
cd frontend
npm run dev
```

### Access the Application
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Docs: http://localhost:8000/docs

## 🔐 First Login

1. Go to http://localhost:3000
2. Login with:
   - Email: `<EMAIL>`
   - Password: `admin123`
3. Create additional users through the admin panel

## 🎯 Key Features

### ✅ Implemented
- **Authentication System** (login only, no signup)
- **Admin User Management** (create, update, delete users)
- **Real-time Agent Updates** via WebSocket
- **Chat History Management** (collapsible left sidebar)
- **File Output Viewer** (collapsible right sidebar)
- **Modern UI** with Tailwind CSS and Shadcn/ui
- **Neon Database Integration**
- **Progress Tracking** for agent execution

### 🔄 Integration Points
- **CrewAI Integration**: Backend service connects to your existing CrewAI setup
- **File Management**: Automatically stores and displays generated reports
- **Real-time Updates**: Live progress updates during agent execution

## 📁 Project Structure

```
gemini/
├── backend/                 # FastAPI backend
│   ├── main.py             # Main application
│   ├── database.py         # Database configuration
│   ├── models/             # Database models
│   ├── api/                # API routes
│   ├── services/           # Business logic
│   └── requirements.txt    # Python dependencies
├── frontend/               # Next.js frontend
│   ├── app/                # App router pages
│   ├── components/         # React components
│   ├── lib/                # Utilities and stores
│   └── package.json        # Node dependencies
└── src/gemini/            # Your existing CrewAI code
```

## 🔧 Customization

### Adding New Agents
1. Update `gemini/src/gemini/config/agents.yaml`
2. Update `gemini/src/gemini/config/tasks.yaml`
3. Modify `gemini/src/gemini/crew.py`
4. The UI will automatically adapt to new agents

### Styling
- Modify `frontend/tailwind.config.js` for theme changes
- Update `frontend/app/globals.css` for custom styles
- Components use Shadcn/ui for consistent design

### Database Schema
- Add new models in `backend/models/models.py`
- Create migrations as needed
- Update API endpoints accordingly

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Verify Neon connection string
   - Check if database is accessible

2. **WebSocket Connection Failed**
   - Ensure backend is running on port 8000
   - Check CORS settings

3. **Authentication Issues**
   - Verify JWT secret key
   - Check token expiration settings

4. **CrewAI Integration Issues**
   - Ensure Python path includes gemini source
   - Verify API keys are set correctly

## 📚 Next Steps

1. **Deploy to Production**
   - Set up proper environment variables
   - Configure production database
   - Set up SSL certificates

2. **Add More Features**
   - File upload functionality
   - Advanced user permissions
   - Agent configuration UI
   - Analytics dashboard

3. **Monitoring**
   - Add logging
   - Set up error tracking
   - Monitor performance

## 🆘 Support

If you encounter any issues:
1. Check the console logs (browser and terminal)
2. Verify all environment variables are set
3. Ensure all dependencies are installed
4. Check the API documentation at http://localhost:8000/docs

The application provides a modern, professional interface for your CrewAI agents with real-time updates, file management, and user administration capabilities.
