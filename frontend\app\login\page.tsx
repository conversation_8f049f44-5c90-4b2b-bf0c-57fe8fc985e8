'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/lib/stores/auth-store'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Bot, Sparkles, Eye, EyeOff, Shield, Zap } from 'lucide-react'

export default function LoginPage() {
  const [email, setEmail] = useState('<EMAIL>')
  const [password, setPassword] = useState('admin123')
  const [error, setError] = useState('')
  const [showPassword, setShowPassword] = useState(false)

  const { login, isLoading, isAuthenticated } = useAuthStore()
  const router = useRouter()

  useEffect(() => {
    if (isAuthenticated) {
      router.push('/dashboard')
    }
  }, [isAuthenticated, router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    if (!email || !password) {
      setError('Please fill in all fields')
      return
    }

    try {
      await login(email, password)
      router.push('/dashboard')
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Login failed. Please try again.')
    }
  }

  return (
    <div className="min-h-screen relative overflow-hidden" style={{ background: 'var(--gradient-dark)' }}>
      {/* Beautiful Background */}
      <div className="absolute inset-0">
        {/* Floating Orbs */}
        <div className="floating-orb"></div>
        <div className="floating-orb"></div>
        <div className="floating-orb"></div>

        {/* Sparkles */}
        <div className="sparkle" style={{ top: '20%', left: '10%' }}></div>
        <div className="sparkle" style={{ top: '60%', right: '15%' }}></div>
        <div className="sparkle" style={{ bottom: '30%', left: '20%' }}></div>
        <div className="sparkle" style={{ top: '40%', right: '30%' }}></div>
        <div className="sparkle" style={{ bottom: '20%', right: '10%' }}></div>

        {/* Grid Pattern */}
        <div
          className="absolute inset-0 opacity-10"
          style={{
            backgroundImage: 'linear-gradient(rgba(56, 189, 248, 0.3) 1px, transparent 1px), linear-gradient(90deg, rgba(56, 189, 248, 0.3) 1px, transparent 1px)',
            backgroundSize: '50px 50px'
          }}
        ></div>
      </div>

      {/* Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-8">
        <div className="w-full max-w-xl animate-slide-up">
          {/* Logo and Header */}
          <div className="text-center mb-16 animate-fade-in">
            <div className="flex items-center justify-center mb-12">
              <div className="relative animate-pulse-glow">
                <div className="w-32 h-32 rounded-full flex items-center justify-center shadow-2xl" style={{ background: 'var(--gradient-primary)' }}>
                  <Bot className="h-16 w-16 text-white" />
                </div>
                <div className="absolute -top-4 -right-4 w-12 h-12 rounded-full flex items-center justify-center animate-bounce" style={{ background: 'var(--gradient-accent)' }}>
                  <Sparkles className="h-6 w-6 text-white" />
                </div>
              </div>
            </div>
            <h1 className="text-7xl font-black mb-6 tracking-tight">
              <span className="gradient-text">Gemini</span>
              <br />
              <span className="text-5xl gradient-text">CrewAI</span>
            </h1>
            <p className="text-white/80 text-2xl font-light mb-8">Next-Generation AI Agent Platform</p>
            <div className="flex items-center justify-center gap-3">
              <div className="w-3 h-3 rounded-full animate-pulse" style={{ background: 'var(--primary-400)' }}></div>
              <div className="w-3 h-3 rounded-full animate-pulse animation-delay-1000" style={{ background: 'var(--secondary-400)' }}></div>
              <div className="w-3 h-3 rounded-full animate-pulse animation-delay-2000" style={{ background: 'var(--accent-400)' }}></div>
            </div>
          </div>

          {/* Login Card */}
          <div className="glass-card p-12 animate-slide-up">
            <div className="text-center mb-10">
              <h2 className="text-4xl font-bold text-white mb-3">Welcome Back</h2>
              <p className="text-white/70 text-xl">Sign in to access your AI agents</p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-8">
              {error && (
                <div className="glass-card p-4 border-red-500/30 bg-red-500/10">
                  <p className="text-red-200 text-center font-medium">{error}</p>
                </div>
              )}

              <div className="space-y-4">
                <label htmlFor="email" className="block text-white font-semibold text-lg">
                  Email Address
                </label>
                <input
                  id="email"
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isLoading}
                  className="glass-input w-full h-16 text-lg"
                />
              </div>

              <div className="space-y-4">
                <label htmlFor="password" className="block text-white font-semibold text-lg">
                  Password
                </label>
                <div className="relative">
                  <input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter your password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    disabled={isLoading}
                    className="glass-input w-full h-16 text-lg pr-16"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-5 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors"
                  >
                    {showPassword ? <EyeOff className="h-6 w-6" /> : <Eye className="h-6 w-6" />}
                  </button>
                </div>
              </div>

              <button
                type="submit"
                className="glass-button w-full h-16 text-xl font-bold"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-3 h-6 w-6 animate-spin" />
                    Signing in...
                  </>
                ) : (
                  <>
                    <Shield className="mr-3 h-6 w-6" />
                    Sign In to CrewAI
                  </>
                )}
              </button>
            </form>

            {/* Demo Credentials */}
            <div className="glass-card p-8 mt-10">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 rounded-xl flex items-center justify-center" style={{ background: 'var(--gradient-accent)' }}>
                  <Zap className="h-6 w-6 text-white" />
                </div>
                <span className="text-2xl font-bold text-white">Demo Access</span>
              </div>
              <div className="text-white/80 space-y-4 text-lg">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Email:</span>
                  <span className="font-mono text-blue-300 font-semibold"><EMAIL></span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-medium">Password:</span>
                  <span className="font-mono text-purple-300 font-semibold">admin123</span>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="text-center mt-8 text-sm text-slate-400">
            <p>Contact your administrator for account access</p>
          </div>
        </div>
      </div>


    </div>
  )
}
