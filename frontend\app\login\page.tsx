'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/lib/stores/auth-store'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Bot, Sparkles, Eye, EyeOff, Shield, Zap } from 'lucide-react'

export default function LoginPage() {
  const [email, setEmail] = useState('<EMAIL>')
  const [password, setPassword] = useState('admin123')
  const [error, setError] = useState('')
  const [showPassword, setShowPassword] = useState(false)

  const { login, isLoading, isAuthenticated } = useAuthStore()
  const router = useRouter()

  useEffect(() => {
    if (isAuthenticated) {
      router.push('/dashboard')
    }
  }, [isAuthenticated, router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    if (!email || !password) {
      setError('Please fill in all fields')
      return
    }

    try {
      await login(email, password)
      router.push('/dashboard')
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Login failed. Please try again.')
    }
  }

  return (
    <div className="min-h-screen relative overflow-hidden" style={{ background: 'var(--gradient-hero)' }}>
      {/* Beautiful Light Background */}
      <div className="absolute inset-0">
        {/* Light Floating Orbs */}
        <div className="light-orb"></div>
        <div className="light-orb"></div>
        <div className="light-orb"></div>

        {/* Decorative Shapes */}
        <div className="decorative-shapes">
          <div className="shape shape-1"></div>
          <div className="shape shape-2"></div>
          <div className="shape shape-3"></div>
        </div>

        {/* Subtle Grid Pattern */}
        <div
          className="absolute inset-0 opacity-5"
          style={{
            backgroundImage: 'linear-gradient(rgba(14, 165, 233, 0.2) 1px, transparent 1px), linear-gradient(90deg, rgba(14, 165, 233, 0.2) 1px, transparent 1px)',
            backgroundSize: '60px 60px'
          }}
        ></div>
      </div>

      {/* Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-8">
        <div className="w-full max-w-2xl animate-slide-up">
          {/* Logo and Header */}
          <div className="text-center mb-20 animate-fade-in">
            <div className="flex items-center justify-center mb-16">
              <div className="relative animate-bounce-gentle">
                <div className="w-40 h-40 rounded-full flex items-center justify-center shadow-2xl modern-card">
                  <div className="w-32 h-32 rounded-full flex items-center justify-center" style={{ background: 'var(--gradient-primary)' }}>
                    <Bot className="h-16 w-16 text-white" />
                  </div>
                </div>
                <div className="absolute -top-6 -right-6 w-16 h-16 rounded-full flex items-center justify-center animate-bounce modern-card" style={{ background: 'var(--gradient-accent)' }}>
                  <Sparkles className="h-8 w-8 text-white" />
                </div>
              </div>
            </div>
            <h1 className="text-8xl font-black mb-8 tracking-tight">
              <span className="gradient-text">Gemini</span>
              <br />
              <span className="text-6xl gradient-text">CrewAI</span>
            </h1>
            <p className="text-neutral-600 text-3xl font-light mb-12 max-w-3xl mx-auto leading-relaxed">
              Next-Generation AI Agent Platform for Intelligent Research & Analysis
            </p>
            <div className="flex items-center justify-center gap-4">
              <div className="w-4 h-4 rounded-full animate-pulse modern-card" style={{ background: 'var(--primary-400)' }}></div>
              <div className="w-4 h-4 rounded-full animate-pulse animation-delay-1000 modern-card" style={{ background: 'var(--secondary-400)' }}></div>
              <div className="w-4 h-4 rounded-full animate-pulse animation-delay-2000 modern-card" style={{ background: 'var(--accent-400)' }}></div>
            </div>
          </div>

          {/* Login Card */}
          <div className="neo-card p-12 animate-slide-up">
            <div className="text-center mb-10">
              <h2 className="text-4xl font-bold text-neutral-900 mb-3">Welcome Back</h2>
              <p className="text-neutral-600 text-lg">Sign in to access your AI agents</p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-8">
              {error && (
                <div className="neo-card p-4 border-red-200 bg-red-50">
                  <p className="text-red-700 text-center font-medium">{error}</p>
                </div>
              )}

              <div className="space-y-3">
                <label htmlFor="email" className="block text-neutral-900 font-semibold text-base">
                  Email Address
                </label>
                <input
                  id="email"
                  type="email"
                  placeholder="Enter your email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isLoading}
                  className="w-full h-14 px-4 text-base bg-white border-2 border-neutral-200 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-200 outline-none"
                />
              </div>

              <div className="space-y-3">
                <label htmlFor="password" className="block text-neutral-900 font-semibold text-base">
                  Password
                </label>
                <div className="relative">
                  <input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter your password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    disabled={isLoading}
                    className="w-full h-14 px-4 pr-12 text-base bg-white border-2 border-neutral-200 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-200 outline-none"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-600 transition-colors"
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                </div>
              </div>

              <button
                type="submit"
                className="neo-button w-full h-14 text-lg font-bold"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-5 w-5 animate-spin" />
                    Signing in...
                  </>
                ) : (
                  <>
                    <Shield className="h-5 w-5" />
                    Sign In to CrewAI
                  </>
                )}
              </button>
            </form>

            {/* Demo Credentials */}
            <div className="neo-card p-6 mt-8">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-10 h-10 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center">
                  <Zap className="h-5 w-5 text-white" />
                </div>
                <span className="text-lg font-bold text-neutral-900">Demo Access</span>
              </div>
              <div className="text-neutral-700 space-y-3 text-sm">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Email:</span>
                  <span className="font-mono text-blue-600 font-semibold"><EMAIL></span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-medium">Password:</span>
                  <span className="font-mono text-purple-600 font-semibold">admin123</span>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="text-center mt-8 text-sm text-slate-400">
            <p>Contact your administrator for account access</p>
          </div>
        </div>
      </div>


    </div>
  )
}
