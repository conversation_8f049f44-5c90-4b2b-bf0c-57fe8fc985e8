Stack trace:
Frame         Function      Args
0007FFFFAEE0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF9DE0) msys-2.0.dll+0x2118E
0007FFFFAEE0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFB1B8) msys-2.0.dll+0x69BA
0007FFFFAEE0  0002100469F2 (00021028DF99, 0007FFFFAD98, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFAEE0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFAEE0  00021006A545 (0007FFFFAEF0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFB1C0  00021006B9A5 (0007FFFFAEF0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8F9B00000 ntdll.dll
7FF8F89C0000 KERNEL32.DLL
7FF8F6E80000 KERNELBASE.dll
7FF8F7CE0000 USER32.dll
7FF8F7590000 win32u.dll
7FF8F8E00000 GDI32.dll
7FF8F73C0000 gdi32full.dll
7FF8F7310000 msvcp_win.dll
7FF8F6D30000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF8F9090000 advapi32.dll
7FF8F8480000 msvcrt.dll
7FF8F8E30000 sechost.dll
7FF8F8CE0000 RPCRT4.dll
7FF8F6280000 CRYPTBASE.DLL
7FF8F6C90000 bcryptPrimitives.dll
7FF8F9050000 IMM32.DLL
