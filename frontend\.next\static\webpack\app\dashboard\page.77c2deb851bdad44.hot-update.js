"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stores/auth-store */ \"(app-pages-browser)/./lib/stores/auth-store.ts\");\n/* harmony import */ var _lib_stores_websocket_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/stores/websocket-store */ \"(app-pages-browser)/./lib/stores/websocket-store.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/dashboard-layout */ \"(app-pages-browser)/./components/layout/dashboard-layout.tsx\");\n/* harmony import */ var _components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/chat/chat-interface */ \"(app-pages-browser)/./components/chat/chat-interface.tsx\");\n/* harmony import */ var _components_chat_chat_history__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/chat/chat-history */ \"(app-pages-browser)/./components/chat/chat-history.tsx\");\n/* harmony import */ var _components_files_file_viewer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/files/file-viewer */ \"(app-pages-browser)/./components/files/file-viewer.tsx\");\n/* harmony import */ var _components_chat_new_chat_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/chat/new-chat-dialog */ \"(app-pages-browser)/./components/chat/new-chat-dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Menu,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Menu,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Menu,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Menu,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Menu,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const { user, isAuthenticated } = (0,_lib_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    const { connect } = (0,_lib_stores_websocket_store__WEBPACK_IMPORTED_MODULE_3__.useWebSocketStore)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [selectedChatId, setSelectedChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isHistoryOpen, setIsHistoryOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFilesOpen, setIsFilesOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewChatOpen, setIsNewChatOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isAuthenticated) {\n            router.push(\"/login\");\n            return;\n        }\n        // Connect to WebSocket\n        if (user === null || user === void 0 ? void 0 : user.id) {\n            connect(user.id);\n        }\n    }, [\n        isAuthenticated,\n        user,\n        router,\n        connect\n    ]);\n    const handleNewChat = ()=>{\n        setIsNewChatOpen(true);\n    };\n    const handleChatCreated = (chatId)=>{\n        setSelectedChatId(chatId);\n        setIsNewChatOpen(false);\n    };\n    const handleChatSelect = (chatId)=>{\n        setSelectedChatId(chatId);\n    };\n    if (!isAuthenticated || !user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_5__.DashboardLayout, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-screen bg-gradient-to-br from-slate-50 to-blue-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"transition-all duration-300 ease-in-out border-r border-slate-200 bg-white/80 backdrop-blur-sm shadow-lg\", isHistoryOpen ? \"w-80\" : \"w-0\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"h-full flex flex-col\", !isHistoryOpen && \"hidden\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-slate-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"font-bold text-xl text-slate-800\",\n                                                    children: \"Chat History\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>setIsHistoryOpen(false),\n                                                    className: \"hover:bg-slate-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                            onClick: handleNewChat,\n                                            className: \"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200\",\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"New Research\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_history__WEBPACK_IMPORTED_MODULE_7__.ChatHistory, {\n                                        selectedChatId: selectedChatId,\n                                        onChatSelect: handleChatSelect\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-16 border-b border-slate-200 bg-white/80 backdrop-blur-sm px-6 flex items-center justify-between shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            !isHistoryOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>setIsHistoryOpen(true),\n                                                className: \"hover:bg-slate-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 114,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"font-bold text-xl text-slate-800\",\n                                                        children: selectedChatId ? \"Research Session\" : \"Gemini CrewAI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>setIsFilesOpen(!isFilesOpen),\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"hover:bg-slate-100 transition-colors\", isFilesOpen && \"bg-blue-100 text-blue-700\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-hidden bg-gradient-to-br from-slate-50 to-blue-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_6__.ChatInterface, {\n                                    chatId: selectedChatId,\n                                    onNewChat: handleNewChat\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"transition-all duration-300 ease-in-out border-l border-slate-200 bg-white/80 backdrop-blur-sm shadow-lg\", isFilesOpen ? \"w-96\" : \"w-0\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"h-full flex flex-col\", !isFilesOpen && \"hidden\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-slate-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"font-bold text-xl text-slate-800\",\n                                                children: \"Generated Files\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>setIsFilesOpen(false),\n                                                className: \"hover:bg-slate-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_files_file_viewer__WEBPACK_IMPORTED_MODULE_8__.FileViewer, {\n                                        chatId: selectedChatId\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_new_chat_dialog__WEBPACK_IMPORTED_MODULE_9__.NewChatDialog, {\n                open: isNewChatOpen,\n                onOpenChange: setIsNewChatOpen,\n                onChatCreated: handleChatCreated\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"PUnEbJnxfipzKwnvNpDngyeCAtc=\", false, function() {\n    return [\n        _lib_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__.useAuthStore,\n        _lib_stores_websocket_store__WEBPACK_IMPORTED_MODULE_3__.useWebSocketStore,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ })

});