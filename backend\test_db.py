#!/usr/bin/env python3
"""
Test database connection and verify admin user exists
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from sqlalchemy.orm import Session
from database import SessionLocal, engine
from models.models import Base, User
from services.auth_service import get_password_hash, verify_password
import uuid

def test_connection():
    """Test database connection"""
    try:
        # Test basic connection
        from sqlalchemy import text
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print("✅ Database connection successful!")
            return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def ensure_admin_user():
    """Ensure admin user exists"""
    db = SessionLocal()
    try:
        # Check if admin user exists
        admin_user = db.query(User).filter(User.email == "<EMAIL>").first()

        if admin_user:
            print("✅ Admin user already exists!")
            # Test password
            if verify_password("admin123", admin_user.hashed_password):
                print("✅ Admin password is correct!")
            else:
                print("❌ Admin password is incorrect!")
            return True
        else:
            print("Creating admin user...")
            # Create admin user
            admin_user = User(
                id=str(uuid.uuid4()),
                email="<EMAIL>",
                username="admin",
                hashed_password=get_password_hash("admin123"),
                full_name="System Administrator",
                is_admin=True,
                is_active=True
            )

            db.add(admin_user)
            db.commit()
            print("✅ Admin user created successfully!")
            return True

    except Exception as e:
        print(f"❌ Error with admin user: {e}")
        db.rollback()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    print("Testing database connection and admin user...")
    print("=" * 50)

    # Test connection
    if not test_connection():
        sys.exit(1)

    # Ensure admin user
    if not ensure_admin_user():
        sys.exit(1)

    print("=" * 50)
    print("✅ All tests passed!")
    print("\nLogin credentials:")
    print("Email: <EMAIL>")
    print("Password: admin123")
