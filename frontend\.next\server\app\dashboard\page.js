/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(rsc)/./app/dashboard/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp%5Cdashboard%5Cpage.tsx&server=true!":
/*!**************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp%5Cdashboard%5Cpage.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(ssr)/./app/dashboard/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDd3l6ZXMlNUNEZXNrdG9wJTVDR29vZ2xlJTVDZ29vZ2xlX2NyZXdhaSU1Q2Zyb250ZW5kJTVDYXBwJTVDZGFzaGJvYXJkJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ2VtaW5pLWNyZXdhaS1mcm9udGVuZC8/MDljYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHd5emVzXFxcXERlc2t0b3BcXFxcR29vZ2xlXFxcXGdvb2dsZV9jcmV3YWlcXFxcZnJvbnRlbmRcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp%5Cdashboard%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp%5Cproviders.tsx&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp%5Cglobals.css&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp%5Cproviders.tsx&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp%5Cglobals.css&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers.tsx */ \"(ssr)/./app/providers.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDd3l6ZXMlNUNEZXNrdG9wJTVDR29vZ2xlJTVDZ29vZ2xlX2NyZXdhaSU1Q2Zyb250ZW5kJTVDYXBwJTVDcHJvdmlkZXJzLnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q3d5emVzJTVDRGVza3RvcCU1Q0dvb2dsZSU1Q2dvb2dsZV9jcmV3YWklNUNmcm9udGVuZCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q3d5emVzJTVDRGVza3RvcCU1Q0dvb2dsZSU1Q2dvb2dsZV9jcmV3YWklNUNmcm9udGVuZCU1Q2FwcCU1Q2dsb2JhbHMuY3NzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2dlbWluaS1jcmV3YWktZnJvbnRlbmQvPzQ2OWIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFx3eXplc1xcXFxEZXNrdG9wXFxcXEdvb2dsZVxcXFxnb29nbGVfY3Jld2FpXFxcXGZyb250ZW5kXFxcXGFwcFxcXFxwcm92aWRlcnMudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp%5Cproviders.tsx&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp%5Cglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDd3l6ZXMlNUNEZXNrdG9wJTVDR29vZ2xlJTVDZ29vZ2xlX2NyZXdhaSU1Q2Zyb250ZW5kJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDYXBwLXJvdXRlci5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q3d5emVzJTVDRGVza3RvcCU1Q0dvb2dsZSU1Q2dvb2dsZV9jcmV3YWklNUNmcm9udGVuZCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDY29tcG9uZW50cyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDd3l6ZXMlNUNEZXNrdG9wJTVDR29vZ2xlJTVDZ29vZ2xlX2NyZXdhaSU1Q2Zyb250ZW5kJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDbGF5b3V0LXJvdXRlci5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q3d5emVzJTVDRGVza3RvcCU1Q0dvb2dsZSU1Q2dvb2dsZV9jcmV3YWklNUNmcm9udGVuZCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDY29tcG9uZW50cyU1Q25vdC1mb3VuZC1ib3VuZGFyeS5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q3d5emVzJTVDRGVza3RvcCU1Q0dvb2dsZSU1Q2dvb2dsZV9jcmV3YWklNUNmcm9udGVuZCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDY29tcG9uZW50cyU1Q3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanMmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUN3eXplcyU1Q0Rlc2t0b3AlNUNHb29nbGUlNUNnb29nbGVfY3Jld2FpJTVDZnJvbnRlbmQlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQTRKO0FBQzVKLDBPQUFnSztBQUNoSyx3T0FBK0o7QUFDL0osa1BBQW9LO0FBQ3BLLHNRQUE4SztBQUM5SyIsInNvdXJjZXMiOlsid2VicGFjazovL2dlbWluaS1jcmV3YWktZnJvbnRlbmQvPzY2ZmEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFx3eXplc1xcXFxEZXNrdG9wXFxcXEdvb2dsZVxcXFxnb29nbGVfY3Jld2FpXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcYXBwLXJvdXRlci5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcd3l6ZXNcXFxcRGVza3RvcFxcXFxHb29nbGVcXFxcZ29vZ2xlX2NyZXdhaVxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFx3eXplc1xcXFxEZXNrdG9wXFxcXEdvb2dsZVxcXFxnb29nbGVfY3Jld2FpXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcd3l6ZXNcXFxcRGVza3RvcFxcXFxHb29nbGVcXFxcZ29vZ2xlX2NyZXdhaVxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG5vdC1mb3VuZC1ib3VuZGFyeS5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcd3l6ZXNcXFxcRGVza3RvcFxcXFxHb29nbGVcXFxcZ29vZ2xlX2NyZXdhaVxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXHJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHd5emVzXFxcXERlc2t0b3BcXFxcR29vZ2xlXFxcXGdvb2dsZV9jcmV3YWlcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stores/auth-store */ \"(ssr)/./lib/stores/auth-store.ts\");\n/* harmony import */ var _lib_stores_websocket_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/stores/websocket-store */ \"(ssr)/./lib/stores/websocket-store.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/dashboard-layout */ \"(ssr)/./components/layout/dashboard-layout.tsx\");\n/* harmony import */ var _components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/chat/chat-interface */ \"(ssr)/./components/chat/chat-interface.tsx\");\n/* harmony import */ var _components_chat_chat_history__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/chat/chat-history */ \"(ssr)/./components/chat/chat-history.tsx\");\n/* harmony import */ var _components_files_file_viewer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/files/file-viewer */ \"(ssr)/./components/files/file-viewer.tsx\");\n/* harmony import */ var _components_chat_new_chat_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/chat/new-chat-dialog */ \"(ssr)/./components/chat/new-chat-dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Plus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Plus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Plus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    const { user, isAuthenticated } = (0,_lib_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    const { connect } = (0,_lib_stores_websocket_store__WEBPACK_IMPORTED_MODULE_3__.useWebSocketStore)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [selectedChatId, setSelectedChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isHistoryOpen, setIsHistoryOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFilesOpen, setIsFilesOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewChatOpen, setIsNewChatOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isAuthenticated) {\n            router.push(\"/login\");\n            return;\n        }\n        // Connect to WebSocket\n        if (user?.id) {\n            connect(user.id);\n        }\n    }, [\n        isAuthenticated,\n        user,\n        router,\n        connect\n    ]);\n    const handleNewChat = ()=>{\n        setIsNewChatOpen(true);\n    };\n    const handleChatCreated = (chatId)=>{\n        setSelectedChatId(chatId);\n        setIsNewChatOpen(false);\n    };\n    const handleChatSelect = (chatId)=>{\n        setSelectedChatId(chatId);\n    };\n    if (!isAuthenticated || !user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_5__.DashboardLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-12 h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-3 h-full bg-white border-r border-gray-200 shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-5 w-5 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 68,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 67,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"font-bold text-xl text-gray-900\",\n                                                        children: \"Conversations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 70,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: handleNewChat,\n                                                className: \"w-full h-12 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg font-semibold text-sm hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center justify-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"New Research\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 overflow-y-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_history__WEBPACK_IMPORTED_MODULE_7__.ChatHistory, {\n                                            selectedChatId: selectedChatId,\n                                            onChatSelect: handleChatSelect\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-6 h-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-16 bg-white/90 backdrop-blur-sm border-b border-gray-200 px-6 flex items-center justify-between shadow-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-10 h-10 rounded-lg flex items-center justify-center bg-gradient-to-r from-blue-500 to-purple-600 shadow-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 100,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 99,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 102,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"font-bold text-lg text-gray-900\",\n                                                                children: selectedChatId ? \"Research Session\" : \"Gemini CrewAI\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 105,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 text-xs\",\n                                                                children: \"AI-Powered Research Platform\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 108,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 104,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1 px-3 py-1 bg-green-100 rounded-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 114,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium text-green-700\",\n                                                            children: \"Online\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 overflow-hidden bg-white\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_6__.ChatInterface, {\n                                            chatId: selectedChatId,\n                                            onNewChat: handleNewChat\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-3 h-full bg-white border-l border-gray-200 shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-pink-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-5 w-5 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"font-bold text-xl text-gray-900\",\n                                                        children: \"Research Files\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        className: \"p-3 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 text-center shadow-sm hover:shadow-md\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4 mx-auto mb-1 text-gray-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 148,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-gray-700\",\n                                                                children: \"Documents\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        className: \"p-3 bg-white rounded-lg border border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-all duration-200 text-center shadow-sm hover:shadow-md\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-4 w-4 mx-auto mb-1 text-gray-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-gray-700\",\n                                                                children: \"Agents\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 overflow-y-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_files_file_viewer__WEBPACK_IMPORTED_MODULE_8__.FileViewer, {\n                                            chatId: selectedChatId\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_new_chat_dialog__WEBPACK_IMPORTED_MODULE_9__.NewChatDialog, {\n                    open: isNewChatOpen,\n                    onOpenChange: setIsNewChatOpen,\n                    onChatCreated: handleChatCreated\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stores/auth-store */ \"(ssr)/./lib/stores/auth-store.ts\");\n/* harmony import */ var _lib_stores_websocket_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/stores/websocket-store */ \"(ssr)/./lib/stores/websocket-store.ts\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\nfunction Providers({ children }) {\n    const { initializeAuth } = (0,_lib_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    const { connect, disconnect } = (0,_lib_stores_websocket_store__WEBPACK_IMPORTED_MODULE_3__.useWebSocketStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize authentication on app start\n        initializeAuth();\n        // Cleanup on unmount\n        return ()=>{\n            disconnect();\n        };\n    }, [\n        initializeAuth,\n        disconnect\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvcHJvdmlkZXJzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUVpQztBQUNxQjtBQUNVO0FBRXpELFNBQVNHLFVBQVUsRUFBRUMsUUFBUSxFQUFpQztJQUNuRSxNQUFNLEVBQUVDLGNBQWMsRUFBRSxHQUFHSixvRUFBWUE7SUFDdkMsTUFBTSxFQUFFSyxPQUFPLEVBQUVDLFVBQVUsRUFBRSxHQUFHTCw4RUFBaUJBO0lBRWpERixnREFBU0EsQ0FBQztRQUNSLHlDQUF5QztRQUN6Q0s7UUFFQSxxQkFBcUI7UUFDckIsT0FBTztZQUNMRTtRQUNGO0lBQ0YsR0FBRztRQUFDRjtRQUFnQkU7S0FBVztJQUUvQixxQkFBTztrQkFBR0g7O0FBQ1oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9nZW1pbmktY3Jld2FpLWZyb250ZW5kLy4vYXBwL3Byb3ZpZGVycy50c3g/Y2U0NiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VBdXRoU3RvcmUgfSBmcm9tICdAL2xpYi9zdG9yZXMvYXV0aC1zdG9yZSdcbmltcG9ydCB7IHVzZVdlYlNvY2tldFN0b3JlIH0gZnJvbSAnQC9saWIvc3RvcmVzL3dlYnNvY2tldC1zdG9yZSdcblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XG4gIGNvbnN0IHsgaW5pdGlhbGl6ZUF1dGggfSA9IHVzZUF1dGhTdG9yZSgpXG4gIGNvbnN0IHsgY29ubmVjdCwgZGlzY29ubmVjdCB9ID0gdXNlV2ViU29ja2V0U3RvcmUoKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gSW5pdGlhbGl6ZSBhdXRoZW50aWNhdGlvbiBvbiBhcHAgc3RhcnRcbiAgICBpbml0aWFsaXplQXV0aCgpXG5cbiAgICAvLyBDbGVhbnVwIG9uIHVubW91bnRcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgZGlzY29ubmVjdCgpXG4gICAgfVxuICB9LCBbaW5pdGlhbGl6ZUF1dGgsIGRpc2Nvbm5lY3RdKVxuXG4gIHJldHVybiA8PntjaGlsZHJlbn08Lz5cbn1cbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VBdXRoU3RvcmUiLCJ1c2VXZWJTb2NrZXRTdG9yZSIsIlByb3ZpZGVycyIsImNoaWxkcmVuIiwiaW5pdGlhbGl6ZUF1dGgiLCJjb25uZWN0IiwiZGlzY29ubmVjdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./components/chat/chat-history.tsx":
/*!******************************************!*\
  !*** ./components/chat/chat-history.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatHistory: () => (/* binding */ ChatHistory)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,MessageSquare!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,MessageSquare!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* __next_internal_client_entry_do_not_use__ ChatHistory auto */ \n\n\n\nfunction ChatHistory({ selectedChatId, onChatSelect }) {\n    const [chats, setChats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // TODO: Fetch chat history from API\n        // For now, show empty state\n        setLoading(false);\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    1,\n                    2,\n                    3\n                ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-16 bg-muted animate-pulse rounded-lg\"\n                    }, i, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                lineNumber: 33,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this);\n    }\n    if (chats.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-8 w-8 text-muted-foreground mx-auto mb-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-muted-foreground\",\n                    children: \"No chat history yet\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-muted-foreground mt-1\",\n                    children: \"Start a new research session to begin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 space-y-2\",\n        children: chats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: `p-3 cursor-pointer transition-colors hover:bg-accent ${selectedChatId === chat.id ? \"bg-accent\" : \"\"}`,\n                onClick: ()=>onChatSelect(chat.id),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium text-sm truncate\",\n                            children: chat.topic\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-xs text-muted-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-3 w-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: new Date(chat.created_at).toLocaleDateString()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: `px-2 py-1 rounded-full text-xs ${chat.status === \"completed\" ? \"bg-green-100 text-green-800\" : chat.status === \"running\" ? \"bg-blue-100 text-blue-800\" : chat.status === \"failed\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"}`,\n                                    children: chat.status\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 11\n                }, this)\n            }, chat.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/chat/chat-history.tsx\n");

/***/ }),

/***/ "(ssr)/./components/chat/chat-interface.tsx":
/*!********************************************!*\
  !*** ./components/chat/chat-interface.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Bot_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Plus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Plus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \n\nfunction ChatInterface({ chatId, onNewChat }) {\n    if (!chatId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex items-center justify-center p-8 relative\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-2xl text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24 h-24 rounded-2xl flex items-center justify-center bg-gradient-to-r from-blue-500 to-purple-600 shadow-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        className: \"h-12 w-12 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center animate-bounce\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"h-4 w-4 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 25,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold mb-4 text-gray-900\",\n                        children: [\n                            \"Welcome to \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                children: \"Gemini CrewAI\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 24\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-8 text-lg leading-relaxed\",\n                        children: \"Start a new research session to see your AI agents in action. Watch as they collaborate to gather and analyze information in real-time.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: onNewChat,\n                        className: \"h-14 px-8 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-bold text-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl mx-auto mb-12 flex items-center justify-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, this),\n                            \"Start New Research\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-900 font-semibold text-sm mb-1\",\n                                        children: \"AI Agents\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-xs\",\n                                        children: \"Smart Collaboration\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-900 font-semibold text-sm mb-1\",\n                                        children: \"Real-time\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-xs\",\n                                        children: \"Live Updates\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gradient-to-r from-pink-500 to-pink-600 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-900 font-semibold text-sm mb-1\",\n                                        children: \"Research\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-xs\",\n                                        children: \"Deep Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-muted-foreground\",\n                        children: [\n                            \"Chat interface for session: \",\n                            chatId\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL2NoYXQvY2hhdC1pbnRlcmZhY2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUt3QztBQU9qQyxTQUFTRSxjQUFjLEVBQUVDLE1BQU0sRUFBRUMsU0FBUyxFQUFzQjtJQUNyRSxJQUFJLENBQUNELFFBQVE7UUFDWCxxQkFDRSw4REFBQ0U7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ04sb0ZBQUdBO3dDQUFDTSxXQUFVOzs7Ozs7Ozs7Ozs4Q0FFakIsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDTCxvRkFBSUE7d0NBQUNLLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTXRCLDhEQUFDQzt3QkFBR0QsV0FBVTs7NEJBQXdDOzBDQUN6Qyw4REFBQ0U7Z0NBQUtGLFdBQVU7MENBQTZFOzs7Ozs7Ozs7Ozs7a0NBRzFHLDhEQUFDRzt3QkFBRUgsV0FBVTtrQ0FBNkM7Ozs7OztrQ0FNMUQsOERBQUNJO3dCQUNDQyxNQUFLO3dCQUNMQyxTQUFTUjt3QkFDVEUsV0FBVTs7MENBRVYsOERBQUNMLG9GQUFJQTtnQ0FBQ0ssV0FBVTs7Ozs7OzRCQUFZOzs7Ozs7O2tDQUs5Qiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDTixvRkFBR0E7NENBQUNNLFdBQVU7Ozs7Ozs7Ozs7O2tEQUVqQiw4REFBQ0c7d0NBQUVILFdBQVU7a0RBQTJDOzs7Ozs7a0RBQ3hELDhEQUFDRzt3Q0FBRUgsV0FBVTtrREFBd0I7Ozs7Ozs7Ozs7OzswQ0FFdkMsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNMLG9GQUFJQTs0Q0FBQ0ssV0FBVTs7Ozs7Ozs7Ozs7a0RBRWxCLDhEQUFDRzt3Q0FBRUgsV0FBVTtrREFBMkM7Ozs7OztrREFDeEQsOERBQUNHO3dDQUFFSCxXQUFVO2tEQUF3Qjs7Ozs7Ozs7Ozs7OzBDQUV2Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ04sb0ZBQUdBOzRDQUFDTSxXQUFVOzs7Ozs7Ozs7OztrREFFakIsOERBQUNHO3dDQUFFSCxXQUFVO2tEQUEyQzs7Ozs7O2tEQUN4RCw4REFBQ0c7d0NBQUVILFdBQVU7a0RBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU1qRDtJQUVBLHFCQUNFLDhEQUFDRDtRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBRWIsNEVBQUNEO3dCQUFJQyxXQUFVOzs0QkFBb0M7NEJBQ3BCSDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTzNDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ2VtaW5pLWNyZXdhaS1mcm9udGVuZC8uL2NvbXBvbmVudHMvY2hhdC9jaGF0LWludGVyZmFjZS50c3g/MDUyMSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nXG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9jYXJkJ1xuaW1wb3J0IHsgQm90LCBQbHVzIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5pbnRlcmZhY2UgQ2hhdEludGVyZmFjZVByb3BzIHtcbiAgY2hhdElkOiBzdHJpbmcgfCBudWxsXG4gIG9uTmV3Q2hhdDogKCkgPT4gdm9pZFxufVxuXG5leHBvcnQgZnVuY3Rpb24gQ2hhdEludGVyZmFjZSh7IGNoYXRJZCwgb25OZXdDaGF0IH06IENoYXRJbnRlcmZhY2VQcm9wcykge1xuICBpZiAoIWNoYXRJZCkge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBwLTggcmVsYXRpdmVcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgbWF4LXctMnhsIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgey8qIFdlbGNvbWUgSWNvbiAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgbWItOFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMjQgaC0yNCByb3VuZGVkLTJ4bCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MDAgdG8tcHVycGxlLTYwMCBzaGFkb3cteGxcIj5cbiAgICAgICAgICAgICAgICA8Qm90IGNsYXNzTmFtZT1cImgtMTIgdy0xMiB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLXRvcC0yIC1yaWdodC0yIHctOCBoLTggYmctZ3JhZGllbnQtdG8tciBmcm9tLXllbGxvdy00MDAgdG8tb3JhbmdlLTUwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYW5pbWF0ZS1ib3VuY2VcIj5cbiAgICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFdlbGNvbWUgVGV4dCAqL31cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIG1iLTQgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgV2VsY29tZSB0byA8c3BhbiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS02MDAgdG8tcHVycGxlLTYwMCBiZy1jbGlwLXRleHQgdGV4dC10cmFuc3BhcmVudFwiPkdlbWluaSBDcmV3QUk8L3NwYW4+XG4gICAgICAgICAgPC9oMj5cblxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItOCB0ZXh0LWxnIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgU3RhcnQgYSBuZXcgcmVzZWFyY2ggc2Vzc2lvbiB0byBzZWUgeW91ciBBSSBhZ2VudHMgaW4gYWN0aW9uLlxuICAgICAgICAgICAgV2F0Y2ggYXMgdGhleSBjb2xsYWJvcmF0ZSB0byBnYXRoZXIgYW5kIGFuYWx5emUgaW5mb3JtYXRpb24gaW4gcmVhbC10aW1lLlxuICAgICAgICAgIDwvcD5cblxuICAgICAgICAgIHsvKiBTdGFydCBCdXR0b24gKi99XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICBvbkNsaWNrPXtvbk5ld0NoYXR9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJoLTE0IHB4LTggYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNTAwIHRvLXB1cnBsZS02MDAgdGV4dC13aGl0ZSByb3VuZGVkLXhsIGZvbnQtYm9sZCB0ZXh0LWxnIGhvdmVyOmZyb20tYmx1ZS02MDAgaG92ZXI6dG8tcHVycGxlLTcwMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgc2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bCBteC1hdXRvIG1iLTEyIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC0yXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cbiAgICAgICAgICAgIFN0YXJ0IE5ldyBSZXNlYXJjaFxuICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgey8qIEZlYXR1cmUgQ2FyZHMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0zIGdhcC00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNiBiZy13aGl0ZSByb3VuZGVkLXhsIGJvcmRlciBib3JkZXItZ3JheS0yMDAgc2hhZG93LXNtIGhvdmVyOnNoYWRvdy1tZCB0cmFuc2l0aW9uLXNoYWRvd1wiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTIgaC0xMiBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MDAgdG8tYmx1ZS02MDAgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvIG1iLTRcIj5cbiAgICAgICAgICAgICAgICA8Qm90IGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktOTAwIGZvbnQtc2VtaWJvbGQgdGV4dC1zbSBtYi0xXCI+QUkgQWdlbnRzPC9wPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIHRleHQteHNcIj5TbWFydCBDb2xsYWJvcmF0aW9uPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNiBiZy13aGl0ZSByb3VuZGVkLXhsIGJvcmRlciBib3JkZXItZ3JheS0yMDAgc2hhZG93LXNtIGhvdmVyOnNoYWRvdy1tZCB0cmFuc2l0aW9uLXNoYWRvd1wiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTIgaC0xMiBiZy1ncmFkaWVudC10by1yIGZyb20tcHVycGxlLTUwMCB0by1wdXJwbGUtNjAwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi00XCI+XG4gICAgICAgICAgICAgICAgPFBsdXMgY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS05MDAgZm9udC1zZW1pYm9sZCB0ZXh0LXNtIG1iLTFcIj5SZWFsLXRpbWU8L3A+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgdGV4dC14c1wiPkxpdmUgVXBkYXRlczwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTYgYmctd2hpdGUgcm91bmRlZC14bCBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHNoYWRvdy1zbSBob3ZlcjpzaGFkb3ctbWQgdHJhbnNpdGlvbi1zaGFkb3dcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgYmctZ3JhZGllbnQtdG8tciBmcm9tLXBpbmstNTAwIHRvLXBpbmstNjAwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi00XCI+XG4gICAgICAgICAgICAgICAgPEJvdCBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTkwMCBmb250LXNlbWlib2xkIHRleHQtc20gbWItMVwiPlJlc2VhcmNoPC9wPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIHRleHQteHNcIj5EZWVwIEFuYWx5c2lzPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBmbGV4IGZsZXgtY29sXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBwLTRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy00eGwgbXgtYXV0b1wiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICB7LyogQ2hhdCBtZXNzYWdlcyB3aWxsIGdvIGhlcmUgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICBDaGF0IGludGVyZmFjZSBmb3Igc2Vzc2lvbjoge2NoYXRJZH1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiQm90IiwiUGx1cyIsIkNoYXRJbnRlcmZhY2UiLCJjaGF0SWQiLCJvbk5ld0NoYXQiLCJkaXYiLCJjbGFzc05hbWUiLCJoMiIsInNwYW4iLCJwIiwiYnV0dG9uIiwidHlwZSIsIm9uQ2xpY2siXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/chat/chat-interface.tsx\n");

/***/ }),

/***/ "(ssr)/./components/chat/new-chat-dialog.tsx":
/*!*********************************************!*\
  !*** ./components/chat/new-chat-dialog.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NewChatDialog: () => (/* binding */ NewChatDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* __next_internal_client_entry_do_not_use__ NewChatDialog auto */ \n\n\n\n\n\n\nfunction NewChatDialog({ open, onOpenChange, onChatCreated }) {\n    const [topic, setTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!topic.trim()) return;\n        setLoading(true);\n        try {\n            // TODO: Call API to start new research\n            // For now, simulate creating a chat\n            const chatId = Math.random().toString(36).substr(2, 9);\n            onChatCreated(chatId);\n            setTopic(\"\");\n        } catch (error) {\n            console.error(\"Error starting research:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!open) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n            className: \"w-full max-w-md mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                children: \"Start New Research\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\new-chat-dialog.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: ()=>onOpenChange(false),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\new-chat-dialog.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\new-chat-dialog.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\new-chat-dialog.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\new-chat-dialog.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"topic\",\n                                        children: \"Research Topic\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\new-chat-dialog.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        id: \"topic\",\n                                        placeholder: \"Enter your research topic...\",\n                                        value: topic,\n                                        onChange: (e)=>setTopic(e.target.value),\n                                        disabled: loading\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\new-chat-dialog.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\new-chat-dialog.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        type: \"button\",\n                                        variant: \"outline\",\n                                        onClick: ()=>onOpenChange(false),\n                                        className: \"flex-1\",\n                                        disabled: loading,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\new-chat-dialog.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        type: \"submit\",\n                                        className: \"flex-1\",\n                                        disabled: loading || !topic.trim(),\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\new-chat-dialog.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Starting...\"\n                                            ]\n                                        }, void 0, true) : \"Start Research\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\new-chat-dialog.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\new-chat-dialog.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\new-chat-dialog.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\new-chat-dialog.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\new-chat-dialog.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\new-chat-dialog.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/chat/new-chat-dialog.tsx\n");

/***/ }),

/***/ "(ssr)/./components/files/file-viewer.tsx":
/*!******************************************!*\
  !*** ./components/files/file-viewer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FileViewer: () => (/* binding */ FileViewer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ FileViewer auto */ \n\n\n\n\nfunction FileViewer({ chatId }) {\n    const [files, setFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (chatId) {\n            // TODO: Fetch files for this chat\n            setLoading(false);\n        }\n    }, [\n        chatId\n    ]);\n    if (!chatId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-8 w-8 text-muted-foreground mx-auto mb-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\files\\\\file-viewer.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-muted-foreground\",\n                    children: \"No chat selected\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\files\\\\file-viewer.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-muted-foreground mt-1\",\n                    children: \"Select a chat to view generated files\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\files\\\\file-viewer.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\files\\\\file-viewer.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this);\n    }\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    1,\n                    2\n                ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-16 bg-muted animate-pulse rounded-lg\"\n                    }, i, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\files\\\\file-viewer.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\files\\\\file-viewer.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\files\\\\file-viewer.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this);\n    }\n    if (files.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-8 w-8 text-muted-foreground mx-auto mb-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\files\\\\file-viewer.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-muted-foreground\",\n                    children: \"No files generated yet\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\files\\\\file-viewer.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-muted-foreground mt-1\",\n                    children: \"Files will appear here when agents complete their work\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\files\\\\file-viewer.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\files\\\\file-viewer.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 space-y-2\",\n        children: files.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"p-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-4 w-4 text-muted-foreground mt-0.5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\files\\\\file-viewer.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium text-sm truncate\",\n                                            children: file.filename\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\files\\\\file-viewer.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                (file.file_size / 1024).toFixed(1),\n                                                \" KB • \",\n                                                file.file_type.toUpperCase()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\files\\\\file-viewer.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\files\\\\file-viewer.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\files\\\\file-viewer.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-3 w-3 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\files\\\\file-viewer.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"View\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\files\\\\file-viewer.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-3 w-3 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\files\\\\file-viewer.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Download\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\files\\\\file-viewer.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\files\\\\file-viewer.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\files\\\\file-viewer.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 11\n                }, this)\n            }, file.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\files\\\\file-viewer.tsx\",\n                lineNumber: 70,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\files\\\\file-viewer.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL2ZpbGVzL2ZpbGUtdmlld2VyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUUyQztBQUNJO0FBQ0o7QUFDVztBQWMvQyxTQUFTTyxXQUFXLEVBQUVDLE1BQU0sRUFBbUI7SUFDcEQsTUFBTSxDQUFDQyxPQUFPQyxTQUFTLEdBQUdWLCtDQUFRQSxDQUFhLEVBQUU7SUFDakQsTUFBTSxDQUFDVyxTQUFTQyxXQUFXLEdBQUdaLCtDQUFRQSxDQUFDO0lBRXZDQyxnREFBU0EsQ0FBQztRQUNSLElBQUlPLFFBQVE7WUFDVixrQ0FBa0M7WUFDbENJLFdBQVc7UUFDYjtJQUNGLEdBQUc7UUFBQ0o7S0FBTztJQUVYLElBQUksQ0FBQ0EsUUFBUTtRQUNYLHFCQUNFLDhEQUFDSztZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ1YsaUdBQVFBO29CQUFDVSxXQUFVOzs7Ozs7OEJBQ3BCLDhEQUFDQztvQkFBRUQsV0FBVTs4QkFBZ0M7Ozs7Ozs4QkFDN0MsOERBQUNDO29CQUFFRCxXQUFVOzhCQUFxQzs7Ozs7Ozs7Ozs7O0lBS3hEO0lBRUEsSUFBSUgsU0FBUztRQUNYLHFCQUNFLDhEQUFDRTtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTswQkFDWjtvQkFBQztvQkFBRztpQkFBRSxDQUFDRSxHQUFHLENBQUMsQ0FBQ0Msa0JBQ1gsOERBQUNKO3dCQUFZQyxXQUFVO3VCQUFiRzs7Ozs7Ozs7Ozs7Ozs7O0lBS3BCO0lBRUEsSUFBSVIsTUFBTVMsTUFBTSxLQUFLLEdBQUc7UUFDdEIscUJBQ0UsOERBQUNMO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDVixpR0FBUUE7b0JBQUNVLFdBQVU7Ozs7Ozs4QkFDcEIsOERBQUNDO29CQUFFRCxXQUFVOzhCQUFnQzs7Ozs7OzhCQUM3Qyw4REFBQ0M7b0JBQUVELFdBQVU7OEJBQXFDOzs7Ozs7Ozs7Ozs7SUFLeEQ7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTtrQkFDWkwsTUFBTU8sR0FBRyxDQUFDLENBQUNHLHFCQUNWLDhEQUFDaEIscURBQUlBO2dCQUFlVyxXQUFVOzBCQUM1Qiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNWLGlHQUFRQTtvQ0FBQ1UsV0FBVTs7Ozs7OzhDQUNwQiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFBZ0NLLEtBQUtDLFFBQVE7Ozs7OztzREFDNUQsOERBQUNQOzRDQUFJQyxXQUFVOztnREFDWEssQ0FBQUEsS0FBS0UsU0FBUyxHQUFHLElBQUcsRUFBR0MsT0FBTyxDQUFDO2dEQUFHO2dEQUFPSCxLQUFLSSxTQUFTLENBQUNDLFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSTNFLDhEQUFDWDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNaLHlEQUFNQTtvQ0FBQ3VCLE1BQUs7b0NBQUtDLFNBQVE7b0NBQVVaLFdBQVU7O3NEQUM1Qyw4REFBQ1IsaUdBQUdBOzRDQUFDUSxXQUFVOzs7Ozs7d0NBQWlCOzs7Ozs7OzhDQUdsQyw4REFBQ1oseURBQU1BO29DQUFDdUIsTUFBSztvQ0FBS0MsU0FBUTtvQ0FBVVosV0FBVTs7c0RBQzVDLDhEQUFDVCxpR0FBUUE7NENBQUNTLFdBQVU7Ozs7Ozt3Q0FBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7ZUFqQmxDSyxLQUFLUSxFQUFFOzs7Ozs7Ozs7O0FBMEIxQiIsInNvdXJjZXMiOlsid2VicGFjazovL2dlbWluaS1jcmV3YWktZnJvbnRlbmQvLi9jb21wb25lbnRzL2ZpbGVzL2ZpbGUtdmlld2VyLnRzeD9kZDU2Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJ1xuaW1wb3J0IHsgQ2FyZCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9jYXJkJ1xuaW1wb3J0IHsgRmlsZVRleHQsIERvd25sb2FkLCBFeWUgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5cbmludGVyZmFjZSBGaWxlSW5mbyB7XG4gIGlkOiBzdHJpbmdcbiAgZmlsZW5hbWU6IHN0cmluZ1xuICBmaWxlX3R5cGU6IHN0cmluZ1xuICBmaWxlX3NpemU6IG51bWJlclxuICBjcmVhdGVkX2F0OiBzdHJpbmdcbn1cblxuaW50ZXJmYWNlIEZpbGVWaWV3ZXJQcm9wcyB7XG4gIGNoYXRJZDogc3RyaW5nIHwgbnVsbFxufVxuXG5leHBvcnQgZnVuY3Rpb24gRmlsZVZpZXdlcih7IGNoYXRJZCB9OiBGaWxlVmlld2VyUHJvcHMpIHtcbiAgY29uc3QgW2ZpbGVzLCBzZXRGaWxlc10gPSB1c2VTdGF0ZTxGaWxlSW5mb1tdPihbXSlcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoY2hhdElkKSB7XG4gICAgICAvLyBUT0RPOiBGZXRjaCBmaWxlcyBmb3IgdGhpcyBjaGF0XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgIH1cbiAgfSwgW2NoYXRJZF0pXG5cbiAgaWYgKCFjaGF0SWQpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgPEZpbGVUZXh0IGNsYXNzTmFtZT1cImgtOCB3LTggdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIG14LWF1dG8gbWItMlwiIC8+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+Tm8gY2hhdCBzZWxlY3RlZDwvcD5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmQgbXQtMVwiPlxuICAgICAgICAgIFNlbGVjdCBhIGNoYXQgdG8gdmlldyBnZW5lcmF0ZWQgZmlsZXNcbiAgICAgICAgPC9wPlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgaWYgKGxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICB7WzEsIDJdLm1hcCgoaSkgPT4gKFxuICAgICAgICAgICAgPGRpdiBrZXk9e2l9IGNsYXNzTmFtZT1cImgtMTYgYmctbXV0ZWQgYW5pbWF0ZS1wdWxzZSByb3VuZGVkLWxnXCIgLz5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICBpZiAoZmlsZXMubGVuZ3RoID09PSAwKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IHRleHQtY2VudGVyXCI+XG4gICAgICAgIDxGaWxlVGV4dCBjbGFzc05hbWU9XCJoLTggdy04IHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBteC1hdXRvIG1iLTJcIiAvPlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPk5vIGZpbGVzIGdlbmVyYXRlZCB5ZXQ8L3A+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIG10LTFcIj5cbiAgICAgICAgICBGaWxlcyB3aWxsIGFwcGVhciBoZXJlIHdoZW4gYWdlbnRzIGNvbXBsZXRlIHRoZWlyIHdvcmtcbiAgICAgICAgPC9wPlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBzcGFjZS15LTJcIj5cbiAgICAgIHtmaWxlcy5tYXAoKGZpbGUpID0+IChcbiAgICAgICAgPENhcmQga2V5PXtmaWxlLmlkfSBjbGFzc05hbWU9XCJwLTNcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGdhcC0yXCI+XG4gICAgICAgICAgICAgIDxGaWxlVGV4dCBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBtdC0wLjVcIiAvPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBtaW4tdy0wXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXNtIHRydW5jYXRlXCI+e2ZpbGUuZmlsZW5hbWV9PC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgICAgeyhmaWxlLmZpbGVfc2l6ZSAvIDEwMjQpLnRvRml4ZWQoMSl9IEtCIOKAoiB7ZmlsZS5maWxlX3R5cGUudG9VcHBlckNhc2UoKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMVwiPlxuICAgICAgICAgICAgICA8QnV0dG9uIHNpemU9XCJzbVwiIHZhcmlhbnQ9XCJvdXRsaW5lXCIgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgPEV5ZSBjbGFzc05hbWU9XCJoLTMgdy0zIG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgIFZpZXdcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDxCdXR0b24gc2l6ZT1cInNtXCIgdmFyaWFudD1cIm91dGxpbmVcIiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICA8RG93bmxvYWQgY2xhc3NOYW1lPVwiaC0zIHctMyBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICBEb3dubG9hZFxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0NhcmQ+XG4gICAgICApKX1cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiQnV0dG9uIiwiQ2FyZCIsIkZpbGVUZXh0IiwiRG93bmxvYWQiLCJFeWUiLCJGaWxlVmlld2VyIiwiY2hhdElkIiwiZmlsZXMiLCJzZXRGaWxlcyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwicCIsIm1hcCIsImkiLCJsZW5ndGgiLCJmaWxlIiwiZmlsZW5hbWUiLCJmaWxlX3NpemUiLCJ0b0ZpeGVkIiwiZmlsZV90eXBlIiwidG9VcHBlckNhc2UiLCJzaXplIiwidmFyaWFudCIsImlkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/files/file-viewer.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/dashboard-layout.tsx":
/*!************************************************!*\
  !*** ./components/layout/dashboard-layout.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardLayout: () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_stores_auth_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/stores/auth-store */ \"(ssr)/./lib/stores/auth-store.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ DashboardLayout auto */ \n\n\n\nfunction DashboardLayout({ children }) {\n    const { isAuthenticated, user } = (0,_lib_stores_auth_store__WEBPACK_IMPORTED_MODULE_1__.useAuthStore)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (!isAuthenticated) {\n            router.push(\"/login\");\n        }\n    }, [\n        isAuthenticated,\n        router\n    ]);\n    if (!isAuthenticated || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/dashboard-layout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 41,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 50,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 70,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 10,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBQ0U7QUFLaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGdXQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ2VtaW5pLWNyZXdhaS1mcm9udGVuZC8uL2NvbXBvbmVudHMvdWkvaW5wdXQudHN4P2RhNzkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuZXhwb3J0IGludGVyZmFjZSBJbnB1dFByb3BzXG4gIGV4dGVuZHMgUmVhY3QuSW5wdXRIVE1MQXR0cmlidXRlczxIVE1MSW5wdXRFbGVtZW50PiB7fVxuXG5jb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgSW5wdXRQcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgdHlwZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxpbnB1dFxuICAgICAgICB0eXBlPXt0eXBlfVxuICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgIFwiZmxleCBoLTEwIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1zbSByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIGZpbGU6Ym9yZGVyLTAgZmlsZTpiZy10cmFuc3BhcmVudCBmaWxlOnRleHQtc20gZmlsZTpmb250LW1lZGl1bSBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwXCIsXG4gICAgICAgICAgY2xhc3NOYW1lXG4gICAgICAgICl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgIClcbiAgfVxuKVxuSW5wdXQuZGlzcGxheU5hbWUgPSBcIklucHV0XCJcblxuZXhwb3J0IHsgSW5wdXQgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJJbnB1dCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJ0eXBlIiwicHJvcHMiLCJyZWYiLCJpbnB1dCIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/label.tsx":
/*!*********************************!*\
  !*** ./components/ui/label.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined));\nLabel.displayName = \"Label\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBQ0U7QUFLaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUN4Qiw4REFBQ0M7UUFDQ0QsS0FBS0E7UUFDTEYsV0FBV0gsOENBQUVBLENBQ1gsOEZBQ0FHO1FBRUQsR0FBR0MsS0FBSzs7Ozs7O0FBSWZILE1BQU1NLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ2VtaW5pLWNyZXdhaS1mcm9udGVuZC8uL2NvbXBvbmVudHMvdWkvbGFiZWwudHN4Pzg4ZWQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuZXhwb3J0IGludGVyZmFjZSBMYWJlbFByb3BzXG4gIGV4dGVuZHMgUmVhY3QuTGFiZWxIVE1MQXR0cmlidXRlczxIVE1MTGFiZWxFbGVtZW50PiB7fVxuXG5jb25zdCBMYWJlbCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTExhYmVsRWxlbWVudCwgTGFiZWxQcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gICAgPGxhYmVsXG4gICAgICByZWY9e3JlZn1cbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwidGV4dC1zbSBmb250LW1lZGl1bSBsZWFkaW5nLW5vbmUgcGVlci1kaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgcGVlci1kaXNhYmxlZDpvcGFjaXR5LTcwXCIsXG4gICAgICAgIGNsYXNzTmFtZVxuICAgICAgKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApXG4pXG5MYWJlbC5kaXNwbGF5TmFtZSA9IFwiTGFiZWxcIlxuXG5leHBvcnQgeyBMYWJlbCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIkxhYmVsIiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInByb3BzIiwicmVmIiwibGFiZWwiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminAPI: () => (/* binding */ adminAPI),\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   authAPI: () => (/* binding */ authAPI),\n/* harmony export */   chatAPI: () => (/* binding */ chatAPI),\n/* harmony export */   crewAPI: () => (/* binding */ crewAPI),\n/* harmony export */   filesAPI: () => (/* binding */ filesAPI)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: `${API_BASE_URL}/api`,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Request interceptor to add auth token\napi.interceptors.request.use((config)=>{\n    // Token will be set by auth store\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor to handle errors\napi.interceptors.response.use((response)=>{\n    return response;\n}, (error)=>{\n    if (error.response?.status === 401) {\n        // Token expired or invalid\n        // The auth store will handle logout\n        window.location.href = \"/login\";\n    }\n    return Promise.reject(error);\n});\n// API functions\nconst authAPI = {\n    login: (email, password)=>api.post(\"/auth/login\", {\n            email,\n            password\n        }),\n    getMe: ()=>api.get(\"/auth/me\"),\n    verifyToken: ()=>api.post(\"/auth/verify-token\")\n};\nconst chatAPI = {\n    getHistory: (limit = 50, offset = 0)=>api.get(`/chat/history?limit=${limit}&offset=${offset}`),\n    getChat: (chatId)=>api.get(`/chat/${chatId}`),\n    deleteChat: (chatId)=>api.delete(`/chat/${chatId}`),\n    getMessages: (chatId)=>api.get(`/chat/${chatId}/messages`)\n};\nconst crewAPI = {\n    execute: (topic)=>api.post(\"/crew/execute\", {\n            topic\n        })\n};\nconst filesAPI = {\n    getChatFiles: (chatId)=>api.get(`/files/chat/${chatId}`),\n    getFile: (fileId)=>api.get(`/files/${fileId}`),\n    downloadFile: (fileId)=>api.get(`/files/${fileId}/download`),\n    deleteFile: (fileId)=>api.delete(`/files/${fileId}`)\n};\nconst adminAPI = {\n    getUsers: (limit = 100, offset = 0)=>api.get(`/admin/users?limit=${limit}&offset=${offset}`),\n    createUser: (userData)=>api.post(\"/admin/users\", userData),\n    updateUser: (userId, userData)=>api.put(`/admin/users/${userId}`, userData),\n    deleteUser: (userId)=>api.delete(`/admin/users/${userId}`)\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./lib/stores/auth-store.ts":
/*!**********************************!*\
  !*** ./lib/stores/auth-store.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)((set, get)=>({\n        user: null,\n        token: null,\n        isLoading: false,\n        isAuthenticated: false,\n        login: async (email, password)=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.api.post(\"/auth/login\", {\n                    email,\n                    password\n                });\n                const { access_token, user } = response.data;\n                set({\n                    token: access_token,\n                    user,\n                    isAuthenticated: true,\n                    isLoading: false\n                });\n                // Set token for future requests\n                _lib_api__WEBPACK_IMPORTED_MODULE_0__.api.defaults.headers.common[\"Authorization\"] = `Bearer ${access_token}`;\n                // Store in localStorage\n                localStorage.setItem(\"auth-token\", access_token);\n                localStorage.setItem(\"auth-user\", JSON.stringify(user));\n            } catch (error) {\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        logout: ()=>{\n            set({\n                user: null,\n                token: null,\n                isAuthenticated: false\n            });\n            // Remove token from API headers\n            delete _lib_api__WEBPACK_IMPORTED_MODULE_0__.api.defaults.headers.common[\"Authorization\"];\n            // Clear localStorage\n            localStorage.removeItem(\"auth-token\");\n            localStorage.removeItem(\"auth-user\");\n        },\n        initializeAuth: async ()=>{\n            try {\n                const token = localStorage.getItem(\"auth-token\");\n                const userStr = localStorage.getItem(\"auth-user\");\n                if (token && userStr) {\n                    const user = JSON.parse(userStr);\n                    // Set token for API requests\n                    _lib_api__WEBPACK_IMPORTED_MODULE_0__.api.defaults.headers.common[\"Authorization\"] = `Bearer ${token}`;\n                    // Verify token is still valid\n                    const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.api.get(\"/auth/me\");\n                    set({\n                        token,\n                        user: response.data,\n                        isAuthenticated: true\n                    });\n                }\n            } catch (error) {\n                // Token is invalid, logout\n                get().logout();\n            }\n        },\n        setLoading: (loading)=>{\n            set({\n                isLoading: loading\n            });\n        }\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/stores/auth-store.ts\n");

/***/ }),

/***/ "(ssr)/./lib/stores/websocket-store.ts":
/*!***************************************!*\
  !*** ./lib/stores/websocket-store.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWebSocketStore: () => (/* binding */ useWebSocketStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n\nconst useWebSocketStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        socket: null,\n        isConnected: false,\n        messages: [],\n        currentProgress: 0,\n        currentStatus: \"idle\",\n        currentAgent: null,\n        connect: (userId)=>{\n            const wsUrl = \"ws://localhost:8000\" || 0;\n            const socket = new WebSocket(`${wsUrl}/ws/${userId}`);\n            socket.onopen = ()=>{\n                console.log(\"WebSocket connected\");\n                set({\n                    isConnected: true\n                });\n                // Send ping to keep connection alive\n                const pingInterval = setInterval(()=>{\n                    if (socket.readyState === WebSocket.OPEN) {\n                        socket.send(JSON.stringify({\n                            type: \"ping\"\n                        }));\n                    } else {\n                        clearInterval(pingInterval);\n                    }\n                }, 30000) // Ping every 30 seconds\n                ;\n            };\n            socket.onmessage = (event)=>{\n                try {\n                    const message = JSON.parse(event.data);\n                    // Handle different message types\n                    switch(message.type){\n                        case \"agent_update\":\n                            set((state)=>({\n                                    messages: [\n                                        ...state.messages,\n                                        message\n                                    ]\n                                }));\n                            break;\n                        case \"progress_update\":\n                            set({\n                                currentProgress: message.progress || 0,\n                                currentStatus: message.status || \"idle\",\n                                currentAgent: message.current_agent || null\n                            });\n                            break;\n                        case \"file_update\":\n                            set((state)=>({\n                                    messages: [\n                                        ...state.messages,\n                                        message\n                                    ]\n                                }));\n                            break;\n                        case \"completion_update\":\n                            set({\n                                currentProgress: message.success ? 100 : 0,\n                                currentStatus: message.success ? \"completed\" : \"failed\",\n                                currentAgent: null\n                            });\n                            set((state)=>({\n                                    messages: [\n                                        ...state.messages,\n                                        message\n                                    ]\n                                }));\n                            break;\n                        case \"pong\":\n                            break;\n                        default:\n                            console.log(\"Unknown message type:\", message.type);\n                    }\n                } catch (error) {\n                    console.error(\"Error parsing WebSocket message:\", error);\n                }\n            };\n            socket.onclose = ()=>{\n                console.log(\"WebSocket disconnected\");\n                set({\n                    isConnected: false,\n                    socket: null\n                });\n            };\n            socket.onerror = (error)=>{\n                console.error(\"WebSocket error:\", error);\n                set({\n                    isConnected: false\n                });\n            };\n            set({\n                socket\n            });\n        },\n        disconnect: ()=>{\n            const { socket } = get();\n            if (socket) {\n                socket.close();\n                set({\n                    socket: null,\n                    isConnected: false\n                });\n            }\n        },\n        sendMessage: (message)=>{\n            const { socket, isConnected } = get();\n            if (socket && isConnected) {\n                socket.send(JSON.stringify(message));\n            }\n        },\n        addMessage: (message)=>{\n            set((state)=>({\n                    messages: [\n                        ...state.messages,\n                        message\n                    ]\n                }));\n        },\n        clearMessages: ()=>{\n            set({\n                messages: [],\n                currentProgress: 0,\n                currentStatus: \"idle\",\n                currentAgent: null\n            });\n        }\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/stores/websocket-store.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatAgentName: () => (/* binding */ formatAgentName),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   getAgentColor: () => (/* binding */ getAgentColor),\n/* harmony export */   getFileIcon: () => (/* binding */ getFileIcon),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatDate(date) {\n    const d = new Date(date);\n    return d.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"short\",\n        day: \"numeric\",\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n    });\n}\nfunction formatFileSize(bytes) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\nfunction getFileIcon(fileType) {\n    switch(fileType?.toLowerCase()){\n        case \"md\":\n        case \"markdown\":\n            return \"\\uD83D\\uDCDD\";\n        case \"json\":\n            return \"\\uD83D\\uDCCB\";\n        case \"txt\":\n            return \"\\uD83D\\uDCC4\";\n        case \"html\":\n            return \"\\uD83C\\uDF10\";\n        case \"pdf\":\n            return \"\\uD83D\\uDCD5\";\n        default:\n            return \"\\uD83D\\uDCC4\";\n    }\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + \"...\";\n}\nfunction getAgentColor(agentName) {\n    const colors = {\n        \"information_retrieval_specialist\": \"bg-blue-100 text-blue-800 border-blue-200\",\n        \"research_analyst\": \"bg-green-100 text-green-800 border-green-200\",\n        \"research_synthesizer\": \"bg-purple-100 text-purple-800 border-purple-200\",\n        \"system\": \"bg-gray-100 text-gray-800 border-gray-200\"\n    };\n    return colors[agentName] || \"bg-gray-100 text-gray-800 border-gray-200\";\n}\nfunction getStatusColor(status) {\n    const colors = {\n        \"pending\": \"bg-yellow-100 text-yellow-800 border-yellow-200\",\n        \"running\": \"bg-blue-100 text-blue-800 border-blue-200\",\n        \"completed\": \"bg-green-100 text-green-800 border-green-200\",\n        \"failed\": \"bg-red-100 text-red-800 border-red-200\"\n    };\n    return colors[status] || \"bg-gray-100 text-gray-800 border-gray-200\";\n}\nfunction formatAgentName(agentName) {\n    return agentName.split(\"_\").map((word)=>word.charAt(0).toUpperCase() + word.slice(1)).join(\" \");\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"b8e53f683406\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9nZW1pbmktY3Jld2FpLWZyb250ZW5kLy4vYXBwL2dsb2JhbHMuY3NzPzNmODgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJiOGU1M2Y2ODM0MDZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Google\google_crewai\frontend\app\dashboard\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./app/providers.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"Gemini CrewAI - Intelligent Agent Platform\",\n    description: \"Modern UI for CrewAI Agent System - Research and Analysis Platform\",\n    keywords: [\n        \"AI\",\n        \"Agents\",\n        \"Research\",\n        \"Analysis\",\n        \"CrewAI\"\n    ],\n    authors: [\n        {\n            name: \"Gemini CrewAI Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-background\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Google\google_crewai\frontend\app\providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Google\google_crewai\frontend\app\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/axios","vendor-chunks/mime-db","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/lucide-react","vendor-chunks/asynckit","vendor-chunks/use-sync-external-store","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/zustand","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/es-define-property","vendor-chunks/gopd","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();