"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stores/auth-store */ \"(app-pages-browser)/./lib/stores/auth-store.ts\");\n/* harmony import */ var _lib_stores_websocket_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/stores/websocket-store */ \"(app-pages-browser)/./lib/stores/websocket-store.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/dashboard-layout */ \"(app-pages-browser)/./components/layout/dashboard-layout.tsx\");\n/* harmony import */ var _components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/chat/chat-interface */ \"(app-pages-browser)/./components/chat/chat-interface.tsx\");\n/* harmony import */ var _components_chat_chat_history__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/chat/chat-history */ \"(app-pages-browser)/./components/chat/chat-history.tsx\");\n/* harmony import */ var _components_files_file_viewer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/files/file-viewer */ \"(app-pages-browser)/./components/files/file-viewer.tsx\");\n/* harmony import */ var _components_chat_new_chat_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/chat/new-chat-dialog */ \"(app-pages-browser)/./components/chat/new-chat-dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Menu,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Menu,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Menu,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Menu,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Menu,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const { user, isAuthenticated } = (0,_lib_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    const { connect } = (0,_lib_stores_websocket_store__WEBPACK_IMPORTED_MODULE_3__.useWebSocketStore)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [selectedChatId, setSelectedChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isHistoryOpen, setIsHistoryOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFilesOpen, setIsFilesOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewChatOpen, setIsNewChatOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isAuthenticated) {\n            router.push(\"/login\");\n            return;\n        }\n        // Connect to WebSocket\n        if (user === null || user === void 0 ? void 0 : user.id) {\n            connect(user.id);\n        }\n    }, [\n        isAuthenticated,\n        user,\n        router,\n        connect\n    ]);\n    const handleNewChat = ()=>{\n        setIsNewChatOpen(true);\n    };\n    const handleChatCreated = (chatId)=>{\n        setSelectedChatId(chatId);\n        setIsNewChatOpen(false);\n    };\n    const handleChatSelect = (chatId)=>{\n        setSelectedChatId(chatId);\n    };\n    if (!isAuthenticated || !user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_5__.DashboardLayout, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-screen relative overflow-hidden\",\n                style: {\n                    background: \"var(--gradient-hero)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"light-orb\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"light-orb\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"light-orb\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"decorative-shapes\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"shape shape-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"shape shape-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"shape shape-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"relative z-10 transition-all duration-500 ease-in-out\", isHistoryOpen ? \"w-80\" : \"w-0\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"h-full flex flex-col neo-card border-r border-neutral-200 rounded-none\", !isHistoryOpen && \"hidden\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-neutral-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"font-bold text-xl text-neutral-900\",\n                                                    children: \"Chat History\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setIsHistoryOpen(false),\n                                                    className: \"p-2 rounded-lg hover:bg-neutral-100 text-neutral-500 hover:text-neutral-800 transition-colors\",\n                                                    title: \"Close chat history\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: handleNewChat,\n                                            className: \"neo-button w-full h-12 text-base font-bold\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"New Research\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_history__WEBPACK_IMPORTED_MODULE_7__.ChatHistory, {\n                                        selectedChatId: selectedChatId,\n                                        onChatSelect: handleChatSelect\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-28 modern-card border-b border-neutral-200 px-12 flex items-center justify-between rounded-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-8\",\n                                        children: [\n                                            !isHistoryOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setIsHistoryOpen(true),\n                                                className: \"p-4 rounded-xl hover:bg-neutral-100 text-neutral-500 hover:text-neutral-800 transition-colors\",\n                                                title: \"Open chat history\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-7 w-7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative animate-bounce-gentle\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 rounded-2xl flex items-center justify-center shadow-2xl modern-card\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-16 h-16 rounded-2xl flex items-center justify-center\",\n                                                                    style: {\n                                                                        background: \"var(--gradient-primary)\"\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-8 w-8 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 129,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 128,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 127,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -top-2 -right-2 w-6 h-6 rounded-full border-3 border-white animate-pulse modern-card\",\n                                                                style: {\n                                                                    background: \"var(--primary-400)\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 132,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 126,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"font-black text-4xl text-neutral-800 gradient-text\",\n                                                                children: selectedChatId ? \"Research Session\" : \"Gemini CrewAI\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 135,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-neutral-600 text-xl font-medium\",\n                                                                children: \"Next-Generation AI Platform\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 138,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setIsFilesOpen(!isFilesOpen),\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"p-4 rounded-xl hover:bg-neutral-100 text-neutral-500 hover:text-neutral-800 transition-all duration-200\", isFilesOpen && \"bg-blue-100 text-blue-600\"),\n                                            title: \"Toggle file viewer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-7 w-7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_6__.ChatInterface, {\n                                    chatId: selectedChatId,\n                                    onNewChat: handleNewChat\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"relative z-10 transition-all duration-500 ease-in-out\", isFilesOpen ? \"w-96\" : \"w-0\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"h-full flex flex-col modern-card border-l border-neutral-200 rounded-none\", !isFilesOpen && \"hidden\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-8 border-b border-neutral-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"font-bold text-3xl text-neutral-800\",\n                                                children: \"Generated Files\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setIsFilesOpen(false),\n                                                className: \"p-3 rounded-xl hover:bg-neutral-100 text-neutral-500 hover:text-neutral-800 transition-colors\",\n                                                title: \"Close file viewer\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Menu_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_files_file_viewer__WEBPACK_IMPORTED_MODULE_8__.FileViewer, {\n                                        chatId: selectedChatId\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_new_chat_dialog__WEBPACK_IMPORTED_MODULE_9__.NewChatDialog, {\n                open: isNewChatOpen,\n                onOpenChange: setIsNewChatOpen,\n                onChatCreated: handleChatCreated\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"PUnEbJnxfipzKwnvNpDngyeCAtc=\", false, function() {\n    return [\n        _lib_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__.useAuthStore,\n        _lib_stores_websocket_store__WEBPACK_IMPORTED_MODULE_3__.useWebSocketStore,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ })

});