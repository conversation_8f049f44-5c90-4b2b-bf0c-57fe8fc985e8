"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stores/auth-store */ \"(app-pages-browser)/./lib/stores/auth-store.ts\");\n/* harmony import */ var _lib_stores_websocket_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/stores/websocket-store */ \"(app-pages-browser)/./lib/stores/websocket-store.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/dashboard-layout */ \"(app-pages-browser)/./components/layout/dashboard-layout.tsx\");\n/* harmony import */ var _components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/chat/chat-interface */ \"(app-pages-browser)/./components/chat/chat-interface.tsx\");\n/* harmony import */ var _components_chat_chat_history__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/chat/chat-history */ \"(app-pages-browser)/./components/chat/chat-history.tsx\");\n/* harmony import */ var _components_files_file_viewer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/files/file-viewer */ \"(app-pages-browser)/./components/files/file-viewer.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const { user, isAuthenticated } = (0,_lib_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    const { connect } = (0,_lib_stores_websocket_store__WEBPACK_IMPORTED_MODULE_3__.useWebSocketStore)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [selectedChatId, setSelectedChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isHistoryOpen, setIsHistoryOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFilesOpen, setIsFilesOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isAuthenticated) {\n            router.push(\"/login\");\n            return;\n        }\n        // Connect to WebSocket\n        if (user === null || user === void 0 ? void 0 : user.id) {\n            connect(user.id);\n        }\n    }, [\n        isAuthenticated,\n        user,\n        router,\n        connect\n    ]);\n    const handleNewChat = ()=>{\n        // Create a new chat immediately and redirect to it\n        const newChatId = \"temp-\".concat(Date.now());\n        setSelectedChatId(newChatId);\n    };\n    const handleChatDeleted = (chatId)=>{\n        if (selectedChatId === chatId) {\n            setSelectedChatId(null);\n        }\n    };\n    const handleChatSelect = (chatId)=>{\n        setSelectedChatId(chatId);\n    };\n    if (!isAuthenticated || !user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_5__.DashboardLayout, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animated-bg\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-screen text-white flex overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-72 sidebar flex flex-col relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-b from-blue-500/5 via-transparent to-purple-500/5 pointer-events-none\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-[#1a1a1a] relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative floating\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/20 pulse-glow\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-4 w-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 72,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-[#111111] animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 74,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"font-bold text-lg gradient-text\",\n                                                        children: \"CrewAI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"Research Platform\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleNewChat,\n                                        className: \"btn-primary w-full flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 transition-transform group-hover:rotate-90 duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"New Research\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-b border-[#1a1a1a]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"p-3 bg-[#1a1a1a] hover:bg-[#222222] rounded-lg transition-all duration-200 group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400 group-hover:text-blue-400 mx-auto transition-colors\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 group-hover:text-gray-400 mt-1 block\",\n                                                    children: \"Search\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"p-3 bg-[#1a1a1a] hover:bg-[#222222] rounded-lg transition-all duration-200 group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400 group-hover:text-purple-400 mx-auto transition-colors\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 group-hover:text-gray-400 mt-1 block\",\n                                                    children: \"Agents\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"p-3 bg-[#1a1a1a] hover:bg-[#222222] rounded-lg transition-all duration-200 group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400 group-hover:text-green-400 mx-auto transition-colors\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 group-hover:text-gray-400 mt-1 block\",\n                                                    children: \"Files\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xs font-medium text-gray-500 uppercase tracking-wider mb-3\",\n                                            children: \"Recent Conversations\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_history__WEBPACK_IMPORTED_MODULE_7__.ChatHistory, {\n                                        selectedChatId: selectedChatId,\n                                        onChatSelect: handleChatSelect,\n                                        onChatDeleted: handleChatDeleted\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-t border-[#1a1a1a]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-xl border border-green-500/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-green-400\",\n                                                    children: \"All Systems Online\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-green-500/70\",\n                                                    children: \"Ready for research\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col glass-dark relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-br from-blue-500/3 via-transparent to-purple-500/3 pointer-events-none\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 left-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl pointer-events-none floating\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-0 right-1/4 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl pointer-events-none floating-delayed\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_6__.ChatInterface, {\n                                chatId: selectedChatId,\n                                onNewChat: handleNewChat\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-72 sidebar flex flex-col relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-b from-purple-500/5 via-transparent to-pink-500/5 pointer-events-none\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-[#1a1a1a] relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"font-bold text-lg gradient-text\",\n                                                children: \"Research Hub\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                title: \"Toggle files panel\",\n                                                onClick: ()=>setIsFilesOpen(!isFilesOpen),\n                                                className: \"p-2 hover:bg-[#1a1a1a] rounded-lg transition-all duration-200 group\",\n                                                children: isFilesOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400 group-hover:text-red-400 transition-colors\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400 group-hover:text-blue-400 transition-colors\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this),\n                                    isFilesOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                className: \"card p-4 text-center group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-5 w-5 mx-auto mb-2 text-blue-400 group-hover:scale-110 transition-transform floating\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium text-blue-300\",\n                                                        children: \"Documents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                className: \"card p-4 text-center group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5 mx-auto mb-2 text-purple-400 group-hover:scale-110 transition-transform floating-delayed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium text-purple-300\",\n                                                        children: \"AI Outputs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this),\n                            isFilesOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_files_file_viewer__WEBPACK_IMPORTED_MODULE_8__.FileViewer, {\n                                    chatId: selectedChatId\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this),\n                            !isFilesOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 flex items-center justify-center p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl flex items-center justify-center mx-auto mb-4 border border-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-8 w-8 text-gray-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-400 mb-1\",\n                                            children: \"Research Hub\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600 mb-4\",\n                                            children: \"Access your files and AI outputs\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setIsFilesOpen(true),\n                                            className: \"text-xs text-blue-400 hover:text-blue-300 font-medium transition-colors\",\n                                            children: \"Expand Panel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"FjjgRXxbDOmwqcnF6iGNE2np7kQ=\", false, function() {\n    return [\n        _lib_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__.useAuthStore,\n        _lib_stores_websocket_store__WEBPACK_IMPORTED_MODULE_3__.useWebSocketStore,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ })

});