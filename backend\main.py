from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
import asyncio
import json
from typing import List, Dict, Any
import uuid
from datetime import datetime

from database import get_db, engine
from models import models
from api import auth, chat, admin, files
from services.crew_service import CrewService
from services.websocket_manager import WebSocketManager

# Create database tables
models.Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="Gemini CrewAI API",
    description="Modern UI for CrewAI Agent System",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Frontend URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

# WebSocket manager
websocket_manager = WebSocketManager()

# Include routers
app.include_router(auth.router, prefix="/api/auth", tags=["authentication"])
app.include_router(chat.router, prefix="/api/chat", tags=["chat"])
app.include_router(admin.router, prefix="/api/admin", tags=["admin"])
app.include_router(files.router, prefix="/api/files", tags=["files"])

@app.get("/")
async def root():
    return {"message": "Gemini CrewAI API is running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.utcnow()}

@app.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    await websocket_manager.connect(websocket, user_id)
    try:
        while True:
            # Keep connection alive and handle incoming messages
            data = await websocket.receive_text()
            message = json.loads(data)

            if message.get("type") == "ping":
                await websocket_manager.send_personal_message(
                    {"type": "pong", "timestamp": datetime.utcnow().isoformat()},
                    user_id
                )
    except WebSocketDisconnect:
        websocket_manager.disconnect(user_id)

@app.post("/api/crew/execute")
async def execute_crew(
    request: dict,
    db: Session = Depends(get_db),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Execute CrewAI with real-time updates via WebSocket"""
    try:
        # Verify user authentication
        from services.auth_service import verify_token
        user = verify_token(credentials.credentials, db)

        # Create new chat session
        chat_id = str(uuid.uuid4())
        topic = request.get("topic", "")

        # Store chat in database
        from models.models import Chat
        chat = Chat(
            id=chat_id,
            user_id=user.id,
            topic=topic,
            status="running",
            created_at=datetime.utcnow()
        )
        db.add(chat)
        db.commit()

        # Initialize crew service
        crew_service = CrewService(websocket_manager, user.id, chat_id)

        # Execute crew asynchronously
        asyncio.create_task(crew_service.execute_crew(topic, db))

        return {
            "data": {
                "chatId": chat_id,
                "status": "started",
                "message": "Crew execution started. Check WebSocket for updates."
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
