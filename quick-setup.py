#!/usr/bin/env python3
"""
Quick setup script for Gemini CrewAI
This script will:
1. Test database connection
2. Create database tables
3. Create admin user
4. Provide next steps
"""

import sys
import os

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_database_connection():
    """Test if we can connect to the Neon database"""
    try:
        from backend.database import engine
        from sqlalchemy import text
        
        print("🔍 Testing database connection...")
        
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1"))
            print("✅ Database connection successful!")
            return True
            
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("\nPlease check:")
        print("1. Your Neon database is running")
        print("2. The DATABASE_URL in backend/.env is correct")
        print("3. Your internet connection is stable")
        return False

def create_tables_and_admin():
    """Create database tables and admin user"""
    try:
        # Import after adding to path
        from backend.database import SessionLocal, engine
        from backend.models.models import Base, User
        from backend.services.auth_service import get_password_hash
        import uuid
        
        print("🏗️  Creating database tables...")
        Base.metadata.create_all(bind=engine)
        print("✅ Database tables created!")
        
        # Create admin user
        print("👤 Creating admin user...")
        db = SessionLocal()
        
        try:
            # Check if admin already exists
            existing_admin = db.query(User).filter(User.email == "<EMAIL>").first()
            if existing_admin:
                print("ℹ️  Admin user already exists!")
                return True
            
            # Create new admin
            admin_user = User(
                id=str(uuid.uuid4()),
                email="<EMAIL>",
                username="admin",
                hashed_password=get_password_hash("admin123"),
                full_name="System Administrator",
                is_admin=True,
                is_active=True
            )
            
            db.add(admin_user)
            db.commit()
            print("✅ Admin user created successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Error creating admin user: {e}")
            db.rollback()
            return False
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Error setting up database: {e}")
        return False

def main():
    print("🚀 Gemini CrewAI Quick Setup")
    print("=" * 40)
    
    # Test database connection
    if not test_database_connection():
        print("\n❌ Setup failed - database connection issue")
        return False
    
    # Create tables and admin user
    if not create_tables_and_admin():
        print("\n❌ Setup failed - database setup issue")
        return False
    
    print("\n🎉 Setup completed successfully!")
    print("\n" + "=" * 50)
    print("ADMIN LOGIN CREDENTIALS")
    print("=" * 50)
    print("Email:    <EMAIL>")
    print("Password: admin123")
    print("=" * 50)
    
    print("\n📋 Next Steps:")
    print("1. Install backend dependencies:")
    print("   cd backend")
    print("   pip install -r requirements.txt")
    print()
    print("2. Install frontend dependencies:")
    print("   cd frontend")
    print("   npm install")
    print()
    print("3. Start the application:")
    print("   # Option 1: Use startup scripts")
    print("   # Windows: start.bat")
    print("   # Mac/Linux: ./start.sh")
    print()
    print("   # Option 2: Manual start")
    print("   # Terminal 1: cd backend && uvicorn main:app --reload")
    print("   # Terminal 2: cd frontend && npm run dev")
    print()
    print("4. Access the application:")
    print("   Frontend: http://localhost:3000")
    print("   Backend:  http://localhost:8000")
    print("   API Docs: http://localhost:8000/docs")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
