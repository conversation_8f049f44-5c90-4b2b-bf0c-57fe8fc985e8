from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from sqlalchemy import desc
from pydantic import BaseModel, EmailStr
from typing import List, Optional

from database import get_db
from services.auth_service import verify_token, create_user, get_password_hash
from models.models import User, Chat

router = APIRouter()
security = HTTPBearer()

# Pydantic models
class CreateUserRequest(BaseModel):
    email: EmailStr
    username: str
    password: str
    full_name: Optional[str] = None
    is_admin: bool = False

class UpdateUserRequest(BaseModel):
    email: Optional[EmailStr] = None
    username: Optional[str] = None
    full_name: Optional[str] = None
    is_active: Optional[bool] = None
    is_admin: Optional[bool] = None

class UserResponse(BaseModel):
    id: str
    email: str
    username: str
    full_name: Optional[str]
    is_active: bool
    is_admin: bool
    created_at: str
    chat_count: int

def verify_admin(credentials: HTTPAuthorizationCredentials, db: Session):
    """Verify user is admin"""
    user = verify_token(credentials.credentials, db)
    if not user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return user

@router.get("/users", response_model=List[UserResponse])
async def get_all_users(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db),
    limit: int = 100,
    offset: int = 0
):
    """Get all users (admin only)"""
    verify_admin(credentials, db)
    
    users = db.query(User).order_by(desc(User.created_at)).offset(offset).limit(limit).all()
    
    result = []
    for user in users:
        chat_count = db.query(Chat).filter(Chat.user_id == user.id).count()
        result.append(UserResponse(
            id=user.id,
            email=user.email,
            username=user.username,
            full_name=user.full_name,
            is_active=user.is_active,
            is_admin=user.is_admin,
            created_at=user.created_at.isoformat(),
            chat_count=chat_count
        ))
    
    return result

@router.post("/users", response_model=UserResponse)
async def create_new_user(
    request: CreateUserRequest,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """Create a new user (admin only)"""
    verify_admin(credentials, db)
    
    user = create_user(
        db=db,
        email=request.email,
        username=request.username,
        password=request.password,
        full_name=request.full_name,
        is_admin=request.is_admin
    )
    
    chat_count = 0  # New user has no chats
    
    return UserResponse(
        id=user.id,
        email=user.email,
        username=user.username,
        full_name=user.full_name,
        is_active=user.is_active,
        is_admin=user.is_admin,
        created_at=user.created_at.isoformat(),
        chat_count=chat_count
    )

@router.put("/users/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: str,
    request: UpdateUserRequest,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """Update a user (admin only)"""
    verify_admin(credentials, db)
    
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Update fields if provided
    if request.email is not None:
        # Check if email is already taken
        existing = db.query(User).filter(User.email == request.email, User.id != user_id).first()
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already taken"
            )
        user.email = request.email
    
    if request.username is not None:
        # Check if username is already taken
        existing = db.query(User).filter(User.username == request.username, User.id != user_id).first()
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already taken"
            )
        user.username = request.username
    
    if request.full_name is not None:
        user.full_name = request.full_name
    
    if request.is_active is not None:
        user.is_active = request.is_active
    
    if request.is_admin is not None:
        user.is_admin = request.is_admin
    
    db.commit()
    db.refresh(user)
    
    chat_count = db.query(Chat).filter(Chat.user_id == user.id).count()
    
    return UserResponse(
        id=user.id,
        email=user.email,
        username=user.username,
        full_name=user.full_name,
        is_active=user.is_active,
        is_admin=user.is_admin,
        created_at=user.created_at.isoformat(),
        chat_count=chat_count
    )

@router.delete("/users/{user_id}")
async def delete_user(
    user_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """Delete a user (admin only)"""
    admin_user = verify_admin(credentials, db)
    
    # Prevent admin from deleting themselves
    if admin_user.id == user_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete your own account"
        )
    
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    db.delete(user)
    db.commit()
    
    return {"message": "User deleted successfully"}
