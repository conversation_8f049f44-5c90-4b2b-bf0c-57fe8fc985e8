from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, status, Response
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional

from database import get_db
from services.auth_service import verify_token
from models.models import File, Chat

router = APIRouter()
security = HTTPBearer()

# Pydantic models
class FileResponse(BaseModel):
    id: str
    filename: str
    file_type: Optional[str]
    file_size: Optional[int]
    created_at: str

class FileDetailResponse(BaseModel):
    id: str
    filename: str
    file_type: Optional[str]
    file_size: Optional[int]
    content: str
    created_at: str

@router.get("/chat/{chat_id}", response_model=List[FileResponse])
async def get_chat_files(
    chat_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """Get all files for a specific chat"""
    user = verify_token(credentials.credentials, db)
    
    # Verify chat belongs to user
    chat = db.query(Chat).filter(
        Chat.id == chat_id,
        Chat.user_id == user.id
    ).first()
    
    if not chat:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat not found"
        )
    
    files = db.query(File).filter(File.chat_id == chat_id).all()
    
    return [
        FileResponse(
            id=file.id,
            filename=file.filename,
            file_type=file.file_type,
            file_size=file.file_size,
            created_at=file.created_at.isoformat()
        )
        for file in files
    ]

@router.get("/{file_id}", response_model=FileDetailResponse)
async def get_file_detail(
    file_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """Get detailed file information including content"""
    user = verify_token(credentials.credentials, db)
    
    file = db.query(File).join(Chat).filter(
        File.id == file_id,
        Chat.user_id == user.id
    ).first()
    
    if not file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )
    
    return FileDetailResponse(
        id=file.id,
        filename=file.filename,
        file_type=file.file_type,
        file_size=file.file_size,
        content=file.content or "",
        created_at=file.created_at.isoformat()
    )

@router.get("/{file_id}/download")
async def download_file(
    file_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """Download file content"""
    user = verify_token(credentials.credentials, db)
    
    file = db.query(File).join(Chat).filter(
        File.id == file_id,
        Chat.user_id == user.id
    ).first()
    
    if not file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )
    
    # Determine content type based on file extension
    content_type = "text/plain"
    if file.file_type == "md":
        content_type = "text/markdown"
    elif file.file_type == "json":
        content_type = "application/json"
    elif file.file_type == "html":
        content_type = "text/html"
    
    return Response(
        content=file.content or "",
        media_type=content_type,
        headers={
            "Content-Disposition": f"attachment; filename={file.filename}"
        }
    )

@router.delete("/{file_id}")
async def delete_file(
    file_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """Delete a file"""
    user = verify_token(credentials.credentials, db)
    
    file = db.query(File).join(Chat).filter(
        File.id == file_id,
        Chat.user_id == user.id
    ).first()
    
    if not file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )
    
    db.delete(file)
    db.commit()
    
    return {"message": "File deleted successfully"}
