'use client'

import { useEffect } from 'react'
import { useAuthStore } from '@/lib/stores/auth-store'
import { useWebSocketStore } from '@/lib/stores/websocket-store'

export function Providers({ children }: { children: React.ReactNode }) {
  const { initializeAuth } = useAuthStore()
  const { connect, disconnect } = useWebSocketStore()

  useEffect(() => {
    // Initialize authentication on app start
    initializeAuth()

    // Cleanup on unmount
    return () => {
      disconnect()
    }
  }, [initializeAuth, disconnect])

  return <>{children}</>
}
