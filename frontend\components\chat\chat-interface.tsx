'use client'

import { Bo<PERSON>, Plus } from 'lucide-react'

interface ChatInterfaceProps {
  chatId: string | null
  onNewChat: () => void
}

export function ChatInterface({ chatId, onNewChat }: ChatInterfaceProps) {
  if (!chatId) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="max-w-2xl text-center px-4">
          <div className="mb-8">
            <h1 className="text-4xl font-semibold text-gray-900 mb-4">
              How can I help you today?
            </h1>
          </div>

          <div className="grid grid-cols-2 gap-4 mb-8">
            <button
              type="button"
              onClick={onNewChat}
              className="p-4 text-left border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors"
            >
              <div className="text-sm font-medium text-gray-900 mb-1">
                Start research project
              </div>
              <div className="text-xs text-gray-600">
                Begin a new AI-powered research session
              </div>
            </button>

            <button
              type="button"
              className="p-4 text-left border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors"
            >
              <div className="text-sm font-medium text-gray-900 mb-1">
                Analyze data
              </div>
              <div className="text-xs text-gray-600">
                Upload and analyze your documents
              </div>
            </button>

            <button
              type="button"
              className="p-4 text-left border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors"
            >
              <div className="text-sm font-medium text-gray-900 mb-1">
                Generate report
              </div>
              <div className="text-xs text-gray-600">
                Create comprehensive research reports
              </div>
            </button>

            <button
              type="button"
              className="p-4 text-left border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors"
            >
              <div className="text-sm font-medium text-gray-900 mb-1">
                Ask questions
              </div>
              <div className="text-xs text-gray-600">
                Get answers from AI research agents
              </div>
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col">
      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="max-w-3xl mx-auto space-y-6">
          {/* Sample messages - replace with actual chat messages */}
          <div className="flex gap-4">
            <div className="w-8 h-8 rounded-full bg-gray-900 flex items-center justify-center flex-shrink-0">
              <Bot className="h-4 w-4 text-white" />
            </div>
            <div className="flex-1">
              <div className="text-sm text-gray-900">
                I'm ready to help you with your research. What would you like to explore today?
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Input Area */}
      <div className="border-t border-gray-200 p-4">
        <div className="max-w-3xl mx-auto">
          <div className="relative">
            <textarea
              placeholder="Message CrewAI..."
              className="w-full resize-none border border-gray-300 rounded-xl px-4 py-3 pr-12 focus:outline-none focus:border-gray-400 text-sm min-h-[44px] max-h-[200px]"
              rows={1}
            />
            <button
              type="button"
              title="Send message"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-gray-900 text-white rounded-lg flex items-center justify-center hover:bg-gray-800 transition-colors"
            >
              <Plus className="h-4 w-4" />
            </button>
          </div>
          <div className="text-xs text-gray-500 mt-2 text-center">
            CrewAI can make mistakes. Consider checking important information.
          </div>
        </div>
      </div>
    </div>
  )
}
