'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Bot, Plus } from 'lucide-react'

interface ChatInterfaceProps {
  chatId: string | null
  onNewChat: () => void
}

export function ChatInterface({ chatId, onNewChat }: ChatInterfaceProps) {
  if (!chatId) {
    return (
      <div className="flex-1 flex items-center justify-center p-16 relative">
        <div className="relative z-10 w-full max-w-6xl animate-slide-up">
          <div className="modern-card p-24 text-center">
            <div className="flex justify-center mb-16">
              <div className="relative animate-bounce-gentle">
                <div className="w-48 h-48 rounded-full flex items-center justify-center shadow-2xl modern-card">
                  <div className="w-40 h-40 rounded-full flex items-center justify-center" style={{ background: 'var(--gradient-primary)' }}>
                    <Bot className="h-20 w-20 text-white" />
                  </div>
                </div>
                <div className="absolute -top-8 -right-8 w-20 h-20 rounded-full flex items-center justify-center animate-bounce modern-card" style={{ background: 'var(--gradient-accent)' }}>
                  <Plus className="h-10 w-10 text-white" />
                </div>
              </div>
            </div>

            <h2 className="text-8xl font-black mb-12 text-neutral-800">
              Welcome to <span className="gradient-text">Gemini CrewAI</span>
            </h2>

            <p className="text-neutral-600 mb-20 text-3xl leading-relaxed max-w-5xl mx-auto font-light">
              Start a new research session to see your AI agents in action.
              Watch as they collaborate to gather, analyze, and synthesize information for you in real-time.
            </p>

            <button
              type="button"
              onClick={onNewChat}
              className="modern-button h-24 px-16 text-3xl font-bold mx-auto block mb-20"
            >
              <Plus className="h-10 w-10 mr-6" />
              Start New Research
            </button>

            <div className="grid grid-cols-3 gap-12 max-w-4xl mx-auto">
              <div className="modern-card p-12 text-center">
                <div className="w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-8" style={{ background: 'var(--gradient-primary)' }}>
                  <Bot className="h-10 w-10 text-white" />
                </div>
                <p className="text-neutral-800 font-bold text-2xl mb-3">AI Agents</p>
                <p className="text-neutral-600 text-xl">Intelligent Collaboration</p>
              </div>
              <div className="modern-card p-12 text-center">
                <div className="w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-8" style={{ background: 'var(--gradient-secondary)' }}>
                  <Plus className="h-10 w-10 text-white" />
                </div>
                <p className="text-neutral-800 font-bold text-2xl mb-3">Real-time</p>
                <p className="text-neutral-600 text-xl">Live Updates</p>
              </div>
              <div className="modern-card p-12 text-center">
                <div className="w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-8" style={{ background: 'var(--gradient-accent)' }}>
                  <Bot className="h-10 w-10 text-white" />
                </div>
                <p className="text-neutral-800 font-bold text-2xl mb-3">Research</p>
                <p className="text-neutral-600 text-xl">Deep Analysis</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col">
      <div className="flex-1 p-4">
        <div className="max-w-4xl mx-auto">
          <div className="space-y-4">
            {/* Chat messages will go here */}
            <div className="text-center text-muted-foreground">
              Chat interface for session: {chatId}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
