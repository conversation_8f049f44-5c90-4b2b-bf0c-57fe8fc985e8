'use client'

import { useState, useEffect, useRef } from 'react'
import { Bot, Plus, FileText, Search, Send, Sparkles, Zap, Target, BarChart3 } from 'lucide-react'
import { chatAPI } from '@/lib/api'

interface ChatInterfaceProps {
  chatId: string | null
  onNewChat: () => void
}

interface Message {
  id: string
  content: string
  role: 'user' | 'assistant'
  created_at: string
}

export function ChatInterface({ chatId, onNewChat }: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([])
  const [loading, setLoading] = useState(false)
  const [input, setInput] = useState('')
  const [sending, setSending] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    if (chatId) {
      loadMessages()
    }
  }, [chatId])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const loadMessages = async () => {
    if (!chatId) return

    setLoading(true)
    try {
      const response = await chatAPI.getMessages(chatId)
      setMessages(response.data || [])
    } catch (error) {
      console.error('Error loading messages:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSend = async () => {
    if (!input.trim() || sending) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: input.trim(),
      role: 'user',
      created_at: new Date().toISOString()
    }

    setMessages(prev => [...prev, userMessage])
    setInput('')
    setSending(true)

    // Simulate AI response (replace with actual API call)
    setTimeout(() => {
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: "I'm processing your request and will provide a comprehensive research analysis shortly...",
        role: 'assistant',
        created_at: new Date().toISOString()
      }
      setMessages(prev => [...prev, aiMessage])
      setSending(false)
    }, 1000)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  if (!chatId) {
    return (
      <div className="flex-1 flex items-center justify-center bg-white">
        <div className="max-w-2xl w-full text-center px-8">
          <div className="mb-8">
            <h1 className="text-4xl font-semibold text-gray-900 mb-4">
              How can I help you today?
            </h1>
          </div>

          <div className="grid grid-cols-2 gap-3 mb-8">
            <button
              type="button"
              onClick={onNewChat}
              className="p-4 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="text-sm font-medium text-gray-900 mb-1">
                Start research project
              </div>
              <div className="text-xs text-gray-600">
                Begin a new AI-powered research session
              </div>
            </button>

            <button
              type="button"
              onClick={onNewChat}
              className="p-4 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="text-sm font-medium text-gray-900 mb-1">
                Analyze data
              </div>
              <div className="text-xs text-gray-600">
                Upload and analyze your documents
              </div>
            </button>

            <button
              type="button"
              onClick={onNewChat}
              className="p-4 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="text-sm font-medium text-gray-900 mb-1">
                Generate report
              </div>
              <div className="text-xs text-gray-600">
                Create comprehensive research reports
              </div>
            </button>

            <button
              type="button"
              onClick={onNewChat}
              className="p-4 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="text-sm font-medium text-gray-900 mb-1">
                Ask questions
              </div>
              <div className="text-xs text-gray-600">
                Get answers from AI research agents
              </div>
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col bg-white">
      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto">
        <div className="max-w-3xl mx-auto px-6 py-8">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="flex items-center gap-3 text-gray-600">
                <div className="w-5 h-5 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                <span>Loading messages...</span>
              </div>
            </div>
          ) : messages.length === 0 ? (
            /* Welcome Message */
            <div className="flex gap-4 mb-8">
              <div className="w-8 h-8 bg-black rounded-lg flex items-center justify-center flex-shrink-0">
                <Bot className="h-4 w-4 text-white" />
              </div>
              <div className="flex-1">
                <div className="text-gray-900">
                  I'm ready to help you with your research. What would you like to explore today?
                </div>
              </div>
            </div>
          ) : (
            /* Messages */
            <div className="space-y-6">
              {messages.map((message) => (
                <div key={message.id} className={`flex gap-4 ${message.role === 'user' ? 'justify-end' : ''}`}>
                  {message.role === 'assistant' && (
                    <div className="w-8 h-8 bg-black rounded-lg flex items-center justify-center flex-shrink-0">
                      <Bot className="h-4 w-4 text-white" />
                    </div>
                  )}
                  <div className={`flex-1 max-w-2xl ${message.role === 'user' ? 'max-w-xl' : ''}`}>
                    <div className={`p-4 rounded-lg ${
                      message.role === 'user'
                        ? 'bg-black text-white ml-12'
                        : 'bg-gray-50 text-gray-900'
                    }`}>
                      <p className="leading-relaxed">
                        {message.content}
                      </p>
                    </div>
                  </div>
                  {message.role === 'user' && (
                    <div className="w-8 h-8 bg-gray-300 rounded-lg flex items-center justify-center flex-shrink-0">
                      <div className="w-5 h-5 bg-gray-500 rounded-full"></div>
                    </div>
                  )}
                </div>
              ))}
              {sending && (
                <div className="flex gap-4">
                  <div className="w-8 h-8 bg-black rounded-lg flex items-center justify-center flex-shrink-0">
                    <Bot className="h-4 w-4 text-white" />
                  </div>
                  <div className="flex-1">
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center gap-3 text-gray-600">
                        <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                        <span>Thinking...</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input Area */}
      <div className="border-t border-gray-200 p-6">
        <div className="max-w-3xl mx-auto">
          <div className="relative">
            <textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Message CrewAI..."
              className="w-full resize-none border border-gray-300 rounded-lg px-4 py-3 pr-12 focus:outline-none focus:border-gray-400 text-gray-900 placeholder-gray-500 min-h-[44px] max-h-[200px]"
              rows={1}
              disabled={sending}
            />
            <button
              type="button"
              onClick={handleSend}
              disabled={!input.trim() || sending}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-black text-white rounded-lg flex items-center justify-center hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {sending ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <Send className="h-4 w-4" />
              )}
            </button>
          </div>
          <div className="text-xs text-gray-500 mt-2 text-center">
            CrewAI can make mistakes. Consider checking important information.
          </div>
        </div>
      </div>
    </div>
  )
}
