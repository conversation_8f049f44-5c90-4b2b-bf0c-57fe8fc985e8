'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Bot, Plus } from 'lucide-react'

interface ChatInterfaceProps {
  chatId: string | null
  onNewChat: () => void
}

export function ChatInterface({ chatId, onNewChat }: ChatInterfaceProps) {
  if (!chatId) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <Card className="w-full max-w-lg shadow-2xl border-0 bg-white/90 backdrop-blur-sm">
          <CardContent className="p-12 text-center">
            <div className="flex justify-center mb-6">
              <div className="relative">
                <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center shadow-2xl">
                  <Bot className="h-12 w-12 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center animate-pulse">
                  <Plus className="h-4 w-4 text-white" />
                </div>
              </div>
            </div>
            <h2 className="text-3xl font-bold mb-3 text-slate-800">Welcome to Gemini CrewAI</h2>
            <p className="text-slate-600 mb-8 text-lg leading-relaxed">
              Start a new research session to see your AI agents in action.
              Watch as they collaborate to gather, analyze, and synthesize information for you.
            </p>
            <Button
              onClick={onNewChat}
              className="w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
            >
              <Plus className="h-5 w-5 mr-2" />
              Start New Research
            </Button>

            <div className="mt-8 grid grid-cols-3 gap-4 text-center">
              <div className="p-3 bg-blue-50 rounded-lg">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <Bot className="h-4 w-4 text-blue-600" />
                </div>
                <p className="text-xs text-slate-600 font-medium">AI Agents</p>
              </div>
              <div className="p-3 bg-purple-50 rounded-lg">
                <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <Plus className="h-4 w-4 text-purple-600" />
                </div>
                <p className="text-xs text-slate-600 font-medium">Real-time</p>
              </div>
              <div className="p-3 bg-green-50 rounded-lg">
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <Bot className="h-4 w-4 text-green-600" />
                </div>
                <p className="text-xs text-slate-600 font-medium">Research</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col">
      <div className="flex-1 p-4">
        <div className="max-w-4xl mx-auto">
          <div className="space-y-4">
            {/* Chat messages will go here */}
            <div className="text-center text-muted-foreground">
              Chat interface for session: {chatId}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
