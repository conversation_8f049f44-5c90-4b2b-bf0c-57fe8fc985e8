'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Bot, Plus } from 'lucide-react'

interface ChatInterfaceProps {
  chatId: string | null
  onNewChat: () => void
}

export function ChatInterface({ chatId, onNewChat }: ChatInterfaceProps) {
  if (!chatId) {
    return (
      <div className="flex-1 flex items-center justify-center p-8 relative">
        <div className="w-full max-w-2xl text-center">
          {/* Welcome Icon */}
          <div className="flex justify-center mb-8">
            <div className="relative">
              <div className="w-24 h-24 rounded-2xl flex items-center justify-center bg-gradient-to-r from-blue-500 to-purple-600 shadow-xl">
                <Bot className="h-12 w-12 text-white" />
              </div>
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center animate-bounce">
                <Plus className="h-4 w-4 text-white" />
              </div>
            </div>
          </div>

          {/* Welcome Text */}
          <h2 className="text-3xl font-bold mb-4 text-gray-900">
            Welcome to <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Gemini CrewAI</span>
          </h2>

          <p className="text-gray-600 mb-8 text-lg leading-relaxed">
            Start a new research session to see your AI agents in action.
            Watch as they collaborate to gather and analyze information in real-time.
          </p>

          {/* Start Button */}
          <button
            type="button"
            onClick={onNewChat}
            className="h-14 px-8 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-bold text-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl mx-auto mb-12 flex items-center justify-center gap-2"
          >
            <Plus className="h-5 w-5" />
            Start New Research
          </button>

          {/* Feature Cards */}
          <div className="grid grid-cols-3 gap-4">
            <div className="p-6 bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Bot className="h-6 w-6 text-white" />
              </div>
              <p className="text-gray-900 font-semibold text-sm mb-1">AI Agents</p>
              <p className="text-gray-600 text-xs">Smart Collaboration</p>
            </div>
            <div className="p-6 bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Plus className="h-6 w-6 text-white" />
              </div>
              <p className="text-gray-900 font-semibold text-sm mb-1">Real-time</p>
              <p className="text-gray-600 text-xs">Live Updates</p>
            </div>
            <div className="p-6 bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-pink-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Bot className="h-6 w-6 text-white" />
              </div>
              <p className="text-gray-900 font-semibold text-sm mb-1">Research</p>
              <p className="text-gray-600 text-xs">Deep Analysis</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col">
      <div className="flex-1 p-4">
        <div className="max-w-4xl mx-auto">
          <div className="space-y-4">
            {/* Chat messages will go here */}
            <div className="text-center text-muted-foreground">
              Chat interface for session: {chatId}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
