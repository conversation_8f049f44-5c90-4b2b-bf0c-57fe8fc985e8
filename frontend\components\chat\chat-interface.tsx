'use client'

import { useState, useEffect, useRef } from 'react'
import { Bot, Plus, FileText, Search, Send, Sparkles, Zap, Target, BarChart3 } from 'lucide-react'
import { chatAPI } from '@/lib/api'

interface ChatInterfaceProps {
  chatId: string | null
  onNewChat: () => void
}

interface Message {
  id: string
  content: string
  role: 'user' | 'assistant'
  created_at: string
}

export function ChatInterface({ chatId, onNewChat }: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([])
  const [loading, setLoading] = useState(false)
  const [input, setInput] = useState('')
  const [sending, setSending] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    if (chatId) {
      loadMessages()
    }
  }, [chatId])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const loadMessages = async () => {
    if (!chatId) return

    setLoading(true)
    try {
      const response = await chatAPI.getMessages(chatId)
      setMessages(response.data || [])
    } catch (error) {
      console.error('Error loading messages:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSend = async () => {
    if (!input.trim() || sending) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: input.trim(),
      role: 'user',
      created_at: new Date().toISOString()
    }

    setMessages(prev => [...prev, userMessage])
    setInput('')
    setSending(true)

    // Simulate AI response (replace with actual API call)
    setTimeout(() => {
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: "I'm processing your request and will provide a comprehensive research analysis shortly...",
        role: 'assistant',
        created_at: new Date().toISOString()
      }
      setMessages(prev => [...prev, aiMessage])
      setSending(false)
    }, 1000)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  if (!chatId) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-violet-50 via-white to-pink-50 relative overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-cyan-400/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        <div className="max-w-6xl w-full text-center px-8 relative z-10">
          {/* Hero Section */}
          <div className="mb-16">
            <div className="flex justify-center mb-8">
              <div className="relative">
                <div className="w-24 h-24 bg-gradient-to-br from-violet-600 via-purple-600 to-pink-600 rounded-3xl flex items-center justify-center shadow-2xl transform rotate-3 hover:rotate-0 transition-transform duration-500">
                  <Sparkles className="h-12 w-12 text-white animate-pulse" />
                </div>
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full animate-bounce"></div>
              </div>
            </div>
            <h1 className="text-6xl font-black text-gray-900 mb-6 leading-tight">
              Welcome to{' '}
              <span className="bg-gradient-to-r from-violet-600 via-purple-600 to-pink-600 bg-clip-text text-transparent animate-pulse">
                CrewAI
              </span>
            </h1>
            <p className="text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-light">
              Your intelligent research companion powered by collaborative AI agents.
              <span className="text-purple-600 font-semibold"> Start exploring limitless possibilities.</span>
            </p>
          </div>

          {/* Action Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            <button
              type="button"
              onClick={onNewChat}
              className="group relative p-8 bg-white/80 backdrop-blur-sm rounded-3xl border border-violet-100 hover:border-violet-300 hover:shadow-2xl transition-all duration-500 text-left transform hover:scale-110 hover:-rotate-1"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-violet-500/10 to-purple-500/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative">
                <div className="w-16 h-16 bg-gradient-to-br from-violet-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform shadow-xl">
                  <Zap className="h-8 w-8 text-white" />
                </div>
                <div className="text-xl font-bold text-gray-900 mb-3">Start Research</div>
                <div className="text-gray-600">Begin a new AI-powered research session</div>
              </div>
            </button>

            <button
              type="button"
              onClick={onNewChat}
              className="group relative p-8 bg-white/80 backdrop-blur-sm rounded-3xl border border-purple-100 hover:border-purple-300 hover:shadow-2xl transition-all duration-500 text-left transform hover:scale-110 hover:rotate-1"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform shadow-xl">
                  <Bot className="h-8 w-8 text-white" />
                </div>
                <div className="text-xl font-bold text-gray-900 mb-3">AI Agents</div>
                <div className="text-gray-600">Deploy specialized research agents</div>
              </div>
            </button>

            <button
              type="button"
              onClick={onNewChat}
              className="group relative p-8 bg-white/80 backdrop-blur-sm rounded-3xl border border-emerald-100 hover:border-emerald-300 hover:shadow-2xl transition-all duration-500 text-left transform hover:scale-110 hover:-rotate-1"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/10 to-teal-500/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative">
                <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform shadow-xl">
                  <Target className="h-8 w-8 text-white" />
                </div>
                <div className="text-xl font-bold text-gray-900 mb-3">Analyze Data</div>
                <div className="text-gray-600">Upload and analyze your documents</div>
              </div>
            </button>

            <button
              type="button"
              onClick={onNewChat}
              className="group relative p-8 bg-white/80 backdrop-blur-sm rounded-3xl border border-orange-100 hover:border-orange-300 hover:shadow-2xl transition-all duration-500 text-left transform hover:scale-110 hover:rotate-1"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-red-500/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative">
                <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform shadow-xl">
                  <BarChart3 className="h-8 w-8 text-white" />
                </div>
                <div className="text-xl font-bold text-gray-900 mb-3">Generate Reports</div>
                <div className="text-gray-600">Create comprehensive research reports</div>
              </div>
            </button>
          </div>

          {/* CTA Button */}
          <button
            type="button"
            onClick={onNewChat}
            className="relative group bg-gradient-to-r from-violet-600 via-purple-600 to-pink-600 text-white px-12 py-6 rounded-2xl font-bold text-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 overflow-hidden"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-pink-600 via-purple-600 to-violet-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="relative flex items-center gap-3">
              <Sparkles className="h-6 w-6 animate-spin" />
              Start Your First Research Session
              <Sparkles className="h-6 w-6 animate-spin" />
            </div>
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col bg-gradient-to-br from-violet-50 via-white to-pink-50 relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 right-20 w-64 h-64 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 left-20 w-64 h-64 bg-gradient-to-br from-blue-400/10 to-cyan-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto relative z-10">
        <div className="max-w-4xl mx-auto px-6 py-8">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="flex items-center gap-3 text-purple-600">
                <div className="w-6 h-6 border-2 border-purple-600 border-t-transparent rounded-full animate-spin"></div>
                <span className="font-medium">Loading messages...</span>
              </div>
            </div>
          ) : messages.length === 0 ? (
            /* Welcome Message */
            <div className="flex gap-4 mb-8">
              <div className="w-12 h-12 bg-gradient-to-br from-violet-600 to-purple-600 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-xl">
                <Sparkles className="h-6 w-6 text-white" />
              </div>
              <div className="flex-1">
                <div className="bg-white/80 backdrop-blur-sm rounded-3xl rounded-tl-lg p-8 border border-violet-100 shadow-xl">
                  <div className="text-gray-900 leading-relaxed">
                    <p className="font-bold text-2xl mb-4 bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent">
                      Welcome to your research session! ✨
                    </p>
                    <p className="text-gray-700 text-lg mb-4">I'm your AI research assistant powered by collaborative agents. I can help you:</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                      <div className="flex items-center gap-3 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl border border-blue-200">
                        <Target className="h-6 w-6 text-blue-600" />
                        <span className="font-medium text-blue-800">Conduct comprehensive research</span>
                      </div>
                      <div className="flex items-center gap-3 p-4 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-2xl border border-emerald-200">
                        <BarChart3 className="h-6 w-6 text-emerald-600" />
                        <span className="font-medium text-emerald-800">Analyze data and insights</span>
                      </div>
                      <div className="flex items-center gap-3 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl border border-purple-200">
                        <FileText className="h-6 w-6 text-purple-600" />
                        <span className="font-medium text-purple-800">Create detailed reports</span>
                      </div>
                      <div className="flex items-center gap-3 p-4 bg-gradient-to-r from-orange-50 to-red-50 rounded-2xl border border-orange-200">
                        <Search className="h-6 w-6 text-orange-600" />
                        <span className="font-medium text-orange-800">Answer complex questions</span>
                      </div>
                    </div>
                    <p className="text-xl font-semibold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent">
                      What would you like to research today? 🚀
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            /* Messages */
            <div className="space-y-6">
              {messages.map((message) => (
                <div key={message.id} className={`flex gap-4 ${message.role === 'user' ? 'justify-end' : ''}`}>
                  {message.role === 'assistant' && (
                    <div className="w-12 h-12 bg-gradient-to-br from-violet-600 to-purple-600 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-xl">
                      <Bot className="h-6 w-6 text-white" />
                    </div>
                  )}
                  <div className={`flex-1 max-w-3xl ${message.role === 'user' ? 'max-w-2xl' : ''}`}>
                    <div className={`p-6 rounded-3xl shadow-lg ${
                      message.role === 'user'
                        ? 'bg-gradient-to-r from-violet-600 to-purple-600 text-white rounded-tr-lg'
                        : 'bg-white/80 backdrop-blur-sm border border-violet-100 rounded-tl-lg'
                    }`}>
                      <p className={`leading-relaxed ${message.role === 'user' ? 'text-white' : 'text-gray-900'}`}>
                        {message.content}
                      </p>
                    </div>
                  </div>
                  {message.role === 'user' && (
                    <div className="w-12 h-12 bg-gradient-to-r from-gray-200 to-gray-300 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg">
                      <div className="w-8 h-8 bg-gradient-to-r from-gray-400 to-gray-500 rounded-full"></div>
                    </div>
                  )}
                </div>
              ))}
              {sending && (
                <div className="flex gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-violet-600 to-purple-600 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-xl">
                    <Bot className="h-6 w-6 text-white animate-pulse" />
                  </div>
                  <div className="flex-1">
                    <div className="bg-white/80 backdrop-blur-sm rounded-3xl rounded-tl-lg p-6 border border-violet-100 shadow-lg">
                      <div className="flex items-center gap-3 text-purple-600">
                        <div className="w-4 h-4 border-2 border-purple-600 border-t-transparent rounded-full animate-spin"></div>
                        <span className="font-medium">AI is thinking...</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input Area */}
      <div className="border-t border-violet-100 bg-white/80 backdrop-blur-xl p-6 relative z-10">
        <div className="max-w-4xl mx-auto">
          <div className="relative">
            <textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me anything about research, data analysis, or any topic you'd like to explore..."
              className="w-full resize-none bg-white/90 backdrop-blur-sm border-2 border-violet-200 rounded-3xl px-6 py-4 pr-16 focus:outline-none focus:ring-4 focus:ring-violet-500/20 focus:border-violet-500 text-gray-900 placeholder-gray-500 transition-all min-h-[60px] max-h-[200px] shadow-xl"
              rows={1}
              disabled={sending}
            />
            <button
              type="button"
              onClick={handleSend}
              disabled={!input.trim() || sending}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 hover:scale-105 w-12 h-12 bg-gradient-to-r from-violet-600 to-purple-600 text-white rounded-2xl flex items-center justify-center hover:from-violet-700 hover:to-purple-700 transition-all duration-200 shadow-xl hover:shadow-2xl disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {sending ? (
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <Send className="h-5 w-5" />
              )}
            </button>
          </div>
          <div className="text-sm text-gray-500 mt-4 text-center">
            <span className="bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent font-semibold">CrewAI</span> uses advanced AI agents to provide comprehensive research. Always verify important information.
          </div>
        </div>
      </div>
    </div>
  )
}
