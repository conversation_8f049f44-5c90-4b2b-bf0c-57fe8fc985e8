'use client'

import { useState, useEffect, useRef } from 'react'
import { Bot, Plus, FileText, Search, Send } from 'lucide-react'
import { chatAPI, crewAPI } from '@/lib/api'

interface ChatInterfaceProps {
  chatId: string | null
  onNewChat: () => void
}

interface Message {
  id: string
  content: string
  role: 'user' | 'assistant'
  created_at: string
}

export function ChatInterface({ chatId, onNewChat }: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([])
  const [loading, setLoading] = useState(false)
  const [input, setInput] = useState('')
  const [sending, setSending] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    if (chatId) {
      loadMessages()
    }
  }, [chatId])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const loadMessages = async () => {
    if (!chatId) return

    setLoading(true)
    try {
      const response = await chatAPI.getMessages(chatId)
      // Convert backend message format to frontend format
      const backendMessages = response.data || []
      const convertedMessages = backendMessages.map((msg: any) => ({
        id: msg.id,
        content: msg.content,
        role: msg.message_type === 'user' ? 'user' : 'assistant',
        created_at: msg.created_at
      }))
      setMessages(convertedMessages)
    } catch (error) {
      console.error('Error loading messages:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSend = async () => {
    if (!input.trim() || sending) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: input.trim(),
      role: 'user',
      created_at: new Date().toISOString()
    }

    setMessages(prev => [...prev, userMessage])
    const messageContent = input.trim()
    setInput('')
    setSending(true)

    try {
      let actualChatId = chatId

      // If this is a temporary chat ID, create a new chat first
      if (chatId?.startsWith('temp-')) {
        const response = await crewAPI.execute(messageContent)
        actualChatId = response.data.data.chatId
        // Update the URL or notify parent component about the new chat ID
        window.history.replaceState(null, '', `/dashboard?chat=${actualChatId}`)
      } else if (chatId) {
        // Send message to existing chat
        await chatAPI.sendMessage(chatId, messageContent)
      }

      // Reload messages to get both user message and AI response
      if (actualChatId && !actualChatId.startsWith('temp-')) {
        await loadMessages()
      }
    } catch (error) {
      console.error('Error sending message:', error)
      // Show error message
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: "Sorry, I encountered an error processing your request. Please try again.",
        role: 'assistant',
        created_at: new Date().toISOString()
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setSending(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  if (!chatId) {
    return (
      <div className="flex-1 flex items-center justify-center bg-[#0a0a0a] relative overflow-hidden">
        {/* Ambient background effects */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/3 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse" />
          <div className="absolute bottom-1/4 right-1/3 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-full blur-3xl" />
        </div>

        <div className="max-w-4xl w-full text-center px-8 relative z-10">
          <div className="mb-16">
            <div className="flex justify-center mb-8">
              <div className="relative">
                <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center shadow-2xl shadow-blue-500/25 animate-pulse">
                  <Bot className="h-10 w-10 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border-2 border-[#0a0a0a] animate-bounce" />
              </div>
            </div>
            <h1 className="text-6xl font-black mb-6 leading-tight">
              <span className="bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent">
                How can I help you today?
              </span>
            </h1>
            <p className="text-xl text-gray-400 max-w-2xl mx-auto leading-relaxed">
              Start a conversation with our AI research team. Choose a research path below or ask anything.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-16">
            <button
              type="button"
              onClick={onNewChat}
              className="card group text-left"
            >
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform shadow-lg shadow-blue-500/25 floating-delayed">
                <Search className="h-6 w-6 text-white" />
              </div>
              <div className="text-lg font-bold text-white mb-2 gradient-text">Start Research Project</div>
              <div className="text-gray-400 text-sm">Begin a new AI-powered research session with collaborative agents</div>
            </button>

            <button
              type="button"
              onClick={onNewChat}
              className="card group text-left"
            >
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform shadow-lg shadow-purple-500/25 floating">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div className="text-lg font-bold text-white mb-2 gradient-text">Analyze Data</div>
              <div className="text-gray-400 text-sm">Upload and analyze your documents with advanced AI insights</div>
            </button>

            <button
              type="button"
              onClick={onNewChat}
              className="card group text-left"
            >
              <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform shadow-lg shadow-emerald-500/25 floating-delayed">
                <Bot className="h-6 w-6 text-white" />
              </div>
              <div className="text-lg font-bold text-white mb-2 gradient-text">Generate Report</div>
              <div className="text-gray-400 text-sm">Create comprehensive research reports and summaries</div>
            </button>

            <button
              type="button"
              onClick={onNewChat}
              className="card group text-left"
            >
              <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform shadow-lg shadow-orange-500/25 floating">
                <Plus className="h-6 w-6 text-white" />
              </div>
              <div className="text-lg font-bold text-white mb-2 gradient-text">Ask Questions</div>
              <div className="text-gray-400 text-sm">Get answers from specialized AI research agents</div>
            </button>
          </div>

          <div className="flex justify-center">
            <button
              type="button"
              onClick={onNewChat}
              className="btn-primary text-lg px-8 py-4 group"
            >
              <div className="flex items-center gap-3">
                <Bot className="h-6 w-6 group-hover:animate-pulse floating" />
                <span className="gradient-text">Start Your Research Journey</span>
                <Plus className="h-6 w-6 group-hover:rotate-90 transition-transform duration-300 floating-delayed" />
              </div>
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col bg-[#0a0a0a] relative">
      {/* Ambient background effects */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-20 right-20 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-20 left-20 w-64 h-64 bg-purple-500/5 rounded-full blur-3xl animate-pulse delay-1000" />
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto relative z-10">
        <div className="max-w-4xl mx-auto px-6 py-8">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="flex items-center gap-3 text-gray-400">
                <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                <span className="font-medium">Loading messages...</span>
              </div>
            </div>
          ) : messages.length === 0 ? (
            /* Welcome Message */
            <div className="flex gap-4 mb-8">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg shadow-blue-500/25">
                <Bot className="h-5 w-5 text-white" />
              </div>
              <div className="flex-1">
                <div className="bg-gradient-to-br from-[#1a1a1a] to-[#111111] rounded-2xl rounded-tl-lg p-6 border border-[#222222] shadow-xl">
                  <div className="text-white leading-relaxed">
                    <p className="font-bold text-xl mb-3 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                      Welcome to your research session! ✨
                    </p>
                    <p className="text-gray-300 mb-4">I'm your AI research assistant powered by collaborative agents. I can help you:</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
                      <div className="flex items-center gap-3 p-3 bg-blue-500/10 rounded-xl border border-blue-500/20">
                        <Search className="h-5 w-5 text-blue-400" />
                        <span className="text-sm font-medium text-blue-300">Conduct comprehensive research</span>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-emerald-500/10 rounded-xl border border-emerald-500/20">
                        <FileText className="h-5 w-5 text-emerald-400" />
                        <span className="text-sm font-medium text-emerald-300">Analyze data and insights</span>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-purple-500/10 rounded-xl border border-purple-500/20">
                        <Bot className="h-5 w-5 text-purple-400" />
                        <span className="text-sm font-medium text-purple-300">Generate detailed reports</span>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-orange-500/10 rounded-xl border border-orange-500/20">
                        <Plus className="h-5 w-5 text-orange-400" />
                        <span className="text-sm font-medium text-orange-300">Answer complex questions</span>
                      </div>
                    </div>
                    <p className="text-lg font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                      What would you like to research today? 🚀
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            /* Messages */
            <div className="space-y-6">
              {messages.map((message) => (
                <div key={message.id} className={`flex gap-4 ${message.role === 'user' ? 'justify-end' : ''}`}>
                  {message.role === 'assistant' && (
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg shadow-blue-500/25">
                      <Bot className="h-5 w-5 text-white" />
                    </div>
                  )}
                  <div className={`flex-1 max-w-3xl ${message.role === 'user' ? 'max-w-2xl' : ''}`}>
                    <div className={message.role === 'user' ? 'message-user' : 'message-ai'}>
                      <p className="leading-relaxed">
                        {message.content}
                      </p>
                    </div>
                  </div>
                  {message.role === 'user' && (
                    <div className="w-10 h-10 bg-gradient-to-br from-gray-600 to-gray-700 rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg">
                      <div className="w-6 h-6 bg-gradient-to-br from-gray-400 to-gray-500 rounded-full"></div>
                    </div>
                  )}
                </div>
              ))}
              {sending && (
                <div className="flex gap-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg shadow-blue-500/25">
                    <Bot className="h-5 w-5 text-white animate-pulse" />
                  </div>
                  <div className="flex-1">
                    <div className="message-ai">
                      <div className="flex items-center gap-3 text-blue-400">
                        <div className="loading-dots">
                          <span></span>
                          <span></span>
                          <span></span>
                        </div>
                        <span className="font-medium">AI is thinking...</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input Area */}
      <div className="border-t border-[#1a1a1a] bg-[#0a0a0a] p-6 relative z-10">
        <div className="max-w-4xl mx-auto">
          <div className="relative">
            <textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me anything about research, data analysis, or any topic you'd like to explore..."
              className="input-modern w-full resize-none pr-16 min-h-[60px] max-h-[200px]"
              rows={1}
              disabled={sending}
            />
            <button
              type="button"
              onClick={handleSend}
              disabled={!input.trim() || sending}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-500 hover:to-purple-500 text-white rounded-xl flex items-center justify-center transition-all duration-200 shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105 active:scale-95"
            >
              {sending ? (
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <Send className="h-5 w-5" />
              )}
            </button>
          </div>
          <div className="text-xs text-gray-500 mt-4 text-center">
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent font-semibold">CrewAI</span> uses advanced AI agents to provide comprehensive research. Always verify important information.
          </div>
        </div>
      </div>
    </div>
  )
}
