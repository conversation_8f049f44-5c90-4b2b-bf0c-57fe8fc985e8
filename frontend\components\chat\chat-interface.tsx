'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Bot, Plus } from 'lucide-react'

interface ChatInterfaceProps {
  chatId: string | null
  onNewChat: () => void
}

export function ChatInterface({ chatId, onNewChat }: ChatInterfaceProps) {
  if (!chatId) {
    return (
      <div className="flex-1 flex items-center justify-center p-12 relative">
        <div className="relative z-10 w-full max-w-4xl animate-slide-up">
          <div className="neo-card p-16 text-center">
            <div className="flex justify-center mb-12">
              <div className="relative">
                <div className="w-32 h-32 rounded-2xl flex items-center justify-center bg-gradient-to-r from-blue-500 to-purple-600 shadow-2xl">
                  <Bot className="h-16 w-16 text-white" />
                </div>
                <div className="absolute -top-4 -right-4 w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center animate-bounce">
                  <Plus className="h-6 w-6 text-white" />
                </div>
              </div>
            </div>

            <h2 className="text-5xl font-bold mb-8 text-neutral-900">
              Welcome to <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Gemini CrewAI</span>
            </h2>

            <p className="text-neutral-600 mb-12 text-xl leading-relaxed max-w-3xl mx-auto">
              Start a new research session to see your AI agents in action.
              Watch as they collaborate to gather, analyze, and synthesize information for you in real-time.
            </p>

            <button
              type="button"
              onClick={onNewChat}
              className="neo-button h-16 px-12 text-xl font-bold mx-auto block mb-12"
            >
              <Plus className="h-6 w-6" />
              Start New Research
            </button>

            <div className="grid grid-cols-3 gap-8 max-w-2xl mx-auto">
              <div className="neo-card p-8 text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-6">
                  <Bot className="h-8 w-8 text-white" />
                </div>
                <p className="text-neutral-900 font-bold text-lg mb-2">AI Agents</p>
                <p className="text-neutral-600 text-sm">Intelligent Collaboration</p>
              </div>
              <div className="neo-card p-8 text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-6">
                  <Plus className="h-8 w-8 text-white" />
                </div>
                <p className="text-neutral-900 font-bold text-lg mb-2">Real-time</p>
                <p className="text-neutral-600 text-sm">Live Updates</p>
              </div>
              <div className="neo-card p-8 text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-pink-500 to-pink-600 rounded-xl flex items-center justify-center mx-auto mb-6">
                  <Bot className="h-8 w-8 text-white" />
                </div>
                <p className="text-neutral-900 font-bold text-lg mb-2">Research</p>
                <p className="text-neutral-600 text-sm">Deep Analysis</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col">
      <div className="flex-1 p-4">
        <div className="max-w-4xl mx-auto">
          <div className="space-y-4">
            {/* Chat messages will go here */}
            <div className="text-center text-muted-foreground">
              Chat interface for session: {chatId}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
