'use client'

import { Bo<PERSON>, Plus } from 'lucide-react'

interface ChatInterfaceProps {
  chatId: string | null
  onNewChat: () => void
}

export function ChatInterface({ chatId, onNewChat }: ChatInterfaceProps) {
  if (!chatId) {
    return (
      <div className="flex-1 flex items-center justify-center bg-black">
        <div className="max-w-2xl w-full text-center px-8">
          <div className="mb-8">
            <h1 className="text-4xl font-semibold text-white mb-4">
              How can I help you today?
            </h1>
          </div>

          <div className="grid grid-cols-2 gap-3 mb-8">
            <button
              type="button"
              onClick={onNewChat}
              className="p-4 text-left bg-zinc-900/50 border border-zinc-800 rounded-xl hover:bg-zinc-800/50 transition-colors"
            >
              <div className="text-sm font-medium text-white mb-1">
                Start research project
              </div>
              <div className="text-xs text-zinc-400">
                Begin a new AI-powered research session
              </div>
            </button>

            <button
              type="button"
              className="p-4 text-left bg-zinc-900/50 border border-zinc-800 rounded-xl hover:bg-zinc-800/50 transition-colors"
            >
              <div className="text-sm font-medium text-white mb-1">
                Analyze data
              </div>
              <div className="text-xs text-zinc-400">
                Upload and analyze your documents
              </div>
            </button>

            <button
              type="button"
              className="p-4 text-left bg-zinc-900/50 border border-zinc-800 rounded-xl hover:bg-zinc-800/50 transition-colors"
            >
              <div className="text-sm font-medium text-white mb-1">
                Generate report
              </div>
              <div className="text-xs text-zinc-400">
                Create comprehensive research reports
              </div>
            </button>

            <button
              type="button"
              className="p-4 text-left bg-zinc-900/50 border border-zinc-800 rounded-xl hover:bg-zinc-800/50 transition-colors"
            >
              <div className="text-sm font-medium text-white mb-1">
                Ask questions
              </div>
              <div className="text-xs text-zinc-400">
                Get answers from AI research agents
              </div>
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col bg-black">
      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto">
        <div className="max-w-3xl mx-auto px-6 py-8">
          {/* AI Message */}
          <div className="flex gap-4 mb-6">
            <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center flex-shrink-0">
              <Bot className="h-4 w-4 text-black" />
            </div>
            <div className="flex-1">
              <div className="text-white">
                I'm ready to help you with your research. What would you like to explore today?
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Input Area */}
      <div className="border-t border-zinc-800/50 p-6">
        <div className="max-w-3xl mx-auto">
          <div className="relative">
            <textarea
              placeholder="Message CrewAI..."
              className="w-full resize-none bg-zinc-900/50 border border-zinc-800 rounded-xl px-4 py-3 pr-12 focus:outline-none focus:border-zinc-600 text-white placeholder-zinc-400 min-h-[44px] max-h-[200px]"
              rows={1}
            />
            <button
              type="button"
              title="Send message"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-white text-black rounded-lg flex items-center justify-center hover:bg-gray-100 transition-colors"
            >
              <Plus className="h-4 w-4" />
            </button>
          </div>
          <div className="text-xs text-zinc-500 mt-2 text-center">
            CrewAI can make mistakes. Consider checking important information.
          </div>
        </div>
      </div>
    </div>
  )
}
