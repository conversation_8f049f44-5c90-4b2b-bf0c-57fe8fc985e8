'use client'

import { Bo<PERSON>, Plus } from 'lucide-react'

interface ChatInterfaceProps {
  chatId: string | null
  onNewChat: () => void
}

export function ChatInterface({ chatId, onNewChat }: ChatInterfaceProps) {
  if (!chatId) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="max-w-4xl w-full text-center px-8">
          {/* Hero Section */}
          <div className="mb-12">
            <div className="flex justify-center mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-xl">
                <Bot className="h-10 w-10 text-white" />
              </div>
            </div>
            <h1 className="text-5xl font-bold text-gray-900 mb-4">
              Welcome to <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">CrewAI</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
              Your AI-powered research platform. Start a conversation and watch our intelligent agents collaborate to deliver comprehensive insights.
            </p>
          </div>

          {/* Action Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <button
              type="button"
              onClick={onNewChat}
              className="group p-6 bg-white rounded-2xl border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-300 text-left"
            >
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                <Plus className="h-6 w-6 text-white" />
              </div>
              <div className="text-lg font-semibold text-gray-900 mb-2">Start Research</div>
              <div className="text-sm text-gray-600">Begin a new AI-powered research session</div>
            </button>

            <button
              type="button"
              className="group p-6 bg-white rounded-2xl border border-gray-200 hover:border-purple-300 hover:shadow-lg transition-all duration-300 text-left"
            >
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                <Bot className="h-6 w-6 text-white" />
              </div>
              <div className="text-lg font-semibold text-gray-900 mb-2">AI Agents</div>
              <div className="text-sm text-gray-600">Deploy specialized research agents</div>
            </button>

            <button
              type="button"
              className="group p-6 bg-white rounded-2xl border border-gray-200 hover:border-green-300 hover:shadow-lg transition-all duration-300 text-left"
            >
              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                <Plus className="h-6 w-6 text-white" />
              </div>
              <div className="text-lg font-semibold text-gray-900 mb-2">Analyze Data</div>
              <div className="text-sm text-gray-600">Upload and analyze your documents</div>
            </button>

            <button
              type="button"
              className="group p-6 bg-white rounded-2xl border border-gray-200 hover:border-orange-300 hover:shadow-lg transition-all duration-300 text-left"
            >
              <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                <Bot className="h-6 w-6 text-white" />
              </div>
              <div className="text-lg font-semibold text-gray-900 mb-2">Generate Reports</div>
              <div className="text-sm text-gray-600">Create comprehensive research reports</div>
            </button>
          </div>

          {/* CTA Button */}
          <button
            type="button"
            onClick={onNewChat}
            className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-2xl font-semibold text-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            Start Your First Research Session
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col bg-white">
      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto">
        <div className="max-w-4xl mx-auto px-6 py-8">
          {/* Welcome Message */}
          <div className="flex gap-4 mb-8">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg">
              <Bot className="h-5 w-5 text-white" />
            </div>
            <div className="flex-1">
              <div className="bg-gray-50 rounded-2xl rounded-tl-lg p-6 border border-gray-100">
                <div className="text-gray-900 leading-relaxed">
                  <p className="font-semibold text-lg mb-2">Welcome to your research session!</p>
                  <p>I'm your AI research assistant. I can help you:</p>
                  <ul className="list-disc list-inside mt-3 space-y-1 text-gray-700">
                    <li>Conduct comprehensive research on any topic</li>
                    <li>Analyze data and generate insights</li>
                    <li>Create detailed reports and summaries</li>
                    <li>Answer complex questions with citations</li>
                  </ul>
                  <p className="mt-4 text-gray-600">What would you like to research today?</p>
                </div>
              </div>
            </div>
          </div>

          {/* Sample conversation - replace with actual messages */}
          <div className="space-y-6">
            {/* User message example */}
            <div className="flex gap-4 justify-end">
              <div className="flex-1 max-w-2xl">
                <div className="bg-blue-600 text-white rounded-2xl rounded-tr-lg p-4 shadow-sm">
                  <p>Research the latest developments in artificial intelligence and machine learning for 2024</p>
                </div>
              </div>
              <div className="w-10 h-10 bg-gray-200 rounded-xl flex items-center justify-center flex-shrink-0">
                <div className="w-6 h-6 bg-gray-400 rounded-full"></div>
              </div>
            </div>

            {/* AI response example */}
            <div className="flex gap-4">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg">
                <Bot className="h-5 w-5 text-white" />
              </div>
              <div className="flex-1">
                <div className="bg-gray-50 rounded-2xl rounded-tl-lg p-6 border border-gray-100">
                  <div className="text-gray-900 leading-relaxed">
                    <p className="font-semibold mb-2">🔍 Research in Progress...</p>
                    <p className="text-gray-600">I'm deploying specialized research agents to gather the latest information on AI and ML developments for 2024. This will include:</p>
                    <div className="mt-3 space-y-2">
                      <div className="flex items-center gap-2 text-sm">
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                        <span>Scanning academic papers and research publications</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                        <span>Analyzing industry reports and trends</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span>Gathering insights from tech conferences and announcements</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Input Area */}
      <div className="border-t border-gray-100 bg-white p-6">
        <div className="max-w-4xl mx-auto">
          <div className="relative">
            <textarea
              placeholder="Ask me anything about research, data analysis, or any topic you'd like to explore..."
              className="w-full resize-none bg-gray-50 border-0 rounded-2xl px-6 py-4 pr-14 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white text-gray-900 placeholder-gray-500 transition-all min-h-[56px] max-h-[200px]"
              rows={1}
            />
            <button
              type="button"
              title="Send message"
              className="absolute right-3 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl flex items-center justify-center hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              <Plus className="h-5 w-5" />
            </button>
          </div>
          <div className="text-xs text-gray-400 mt-3 text-center">
            CrewAI uses advanced AI agents to provide comprehensive research. Always verify important information.
          </div>
        </div>
      </div>
    </div>
  )
}
