'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Bot, Plus } from 'lucide-react'

interface ChatInterfaceProps {
  chatId: string | null
  onNewChat: () => void
}

export function ChatInterface({ chatId, onNewChat }: ChatInterfaceProps) {
  if (!chatId) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <div className="flex justify-center mb-4">
              <Bot className="h-16 w-16 text-muted-foreground" />
            </div>
            <h2 className="text-2xl font-semibold mb-2">Welcome to Gemini CrewAI</h2>
            <p className="text-muted-foreground mb-6">
              Start a new research session to see your AI agents in action
            </p>
            <Button onClick={onNewChat} className="w-full">
              <Plus className="h-4 w-4 mr-2" />
              Start New Research
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col">
      <div className="flex-1 p-4">
        <div className="max-w-4xl mx-auto">
          <div className="space-y-4">
            {/* Chat messages will go here */}
            <div className="text-center text-muted-foreground">
              Chat interface for session: {chatId}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
