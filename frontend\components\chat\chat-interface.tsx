'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Bot, Plus } from 'lucide-react'

interface ChatInterfaceProps {
  chatId: string | null
  onNewChat: () => void
}

export function ChatInterface({ chatId, onNewChat }: ChatInterfaceProps) {
  if (!chatId) {
    return (
      <div className="flex-1 flex items-center justify-center p-8 relative">
        {/* Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-r from-blue-400/10 to-purple-600/10 rounded-full filter blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-gradient-to-r from-purple-400/10 to-pink-600/10 rounded-full filter blur-3xl"></div>
        </div>

        <div className="relative z-10 w-full max-w-2xl">
          <div className="relative group">
            <div className="absolute -inset-1 bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl blur opacity-25 group-hover:opacity-75 transition duration-1000"></div>
            <Card className="relative backdrop-blur-2xl bg-white/5 border border-white/10 shadow-2xl rounded-3xl overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"></div>
              <CardContent className="relative p-16 text-center">
                <div className="flex justify-center mb-8">
                  <div className="relative group">
                    <div className="absolute -inset-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full blur opacity-75 group-hover:opacity-100 transition duration-1000 animate-pulse"></div>
                    <div className="relative w-32 h-32 bg-gradient-to-br from-blue-500 via-purple-600 to-pink-600 rounded-full flex items-center justify-center shadow-2xl">
                      <Bot className="h-16 w-16 text-white" />
                    </div>
                    <div className="absolute -top-4 -right-4 w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center animate-bounce">
                      <Plus className="h-6 w-6 text-white" />
                    </div>
                  </div>
                </div>

                <h2 className="text-5xl font-black mb-4 text-white">
                  Welcome to <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">Gemini CrewAI</span>
                </h2>

                <p className="text-slate-300 mb-12 text-xl leading-relaxed max-w-2xl mx-auto">
                  Start a new research session to see your AI agents in action.
                  Watch as they collaborate to gather, analyze, and synthesize information for you in real-time.
                </p>

                <Button
                  onClick={onNewChat}
                  className="w-full max-w-md h-16 bg-gradient-to-r from-blue-500 via-purple-600 to-pink-600 hover:from-blue-600 hover:via-purple-700 hover:to-pink-700 text-white font-bold text-xl shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-[1.02] rounded-2xl mx-auto"
                >
                  <Plus className="h-6 w-6 mr-3" />
                  Start New Research
                </Button>

                <div className="mt-12 grid grid-cols-3 gap-6 max-w-lg mx-auto">
                  <div className="p-6 bg-white/5 rounded-2xl border border-white/10 backdrop-blur-sm">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                      <Bot className="h-6 w-6 text-white" />
                    </div>
                    <p className="text-white font-bold">AI Agents</p>
                    <p className="text-slate-400 text-sm mt-1">Intelligent</p>
                  </div>
                  <div className="p-6 bg-white/5 rounded-2xl border border-white/10 backdrop-blur-sm">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                      <Plus className="h-6 w-6 text-white" />
                    </div>
                    <p className="text-white font-bold">Real-time</p>
                    <p className="text-slate-400 text-sm mt-1">Live Updates</p>
                  </div>
                  <div className="p-6 bg-white/5 rounded-2xl border border-white/10 backdrop-blur-sm">
                    <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                      <Bot className="h-6 w-6 text-white" />
                    </div>
                    <p className="text-white font-bold">Research</p>
                    <p className="text-slate-400 text-sm mt-1">Deep Analysis</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col">
      <div className="flex-1 p-4">
        <div className="max-w-4xl mx-auto">
          <div className="space-y-4">
            {/* Chat messages will go here */}
            <div className="text-center text-muted-foreground">
              Chat interface for session: {chatId}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
