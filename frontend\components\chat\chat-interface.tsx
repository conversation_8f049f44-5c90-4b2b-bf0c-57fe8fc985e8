'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Bot, Plus } from 'lucide-react'

interface ChatInterfaceProps {
  chatId: string | null
  onNewChat: () => void
}

export function ChatInterface({ chatId, onNewChat }: ChatInterfaceProps) {
  if (!chatId) {
    return (
      <div className="flex-1 flex items-center justify-center p-12 relative">
        <div className="relative z-10 w-full max-w-4xl animate-slide-up">
          <div className="glass-card p-20 text-center">
            <div className="flex justify-center mb-12">
              <div className="relative animate-pulse-glow">
                <div className="w-40 h-40 rounded-full flex items-center justify-center shadow-2xl" style={{ background: 'var(--gradient-primary)' }}>
                  <Bot className="h-20 w-20 text-white" />
                </div>
                <div className="absolute -top-6 -right-6 w-16 h-16 rounded-full flex items-center justify-center animate-bounce" style={{ background: 'var(--gradient-accent)' }}>
                  <Plus className="h-8 w-8 text-white" />
                </div>
              </div>
            </div>

            <h2 className="text-7xl font-black mb-8 text-white">
              Welcome to <span className="gradient-text">Gemini CrewAI</span>
            </h2>

            <p className="text-white/80 mb-16 text-2xl leading-relaxed max-w-4xl mx-auto font-light">
              Start a new research session to see your AI agents in action.
              Watch as they collaborate to gather, analyze, and synthesize information for you in real-time.
            </p>

            <button
              onClick={onNewChat}
              className="glass-button h-20 px-12 text-2xl font-bold mx-auto block mb-16"
            >
              <Plus className="h-8 w-8 mr-4" />
              Start New Research
            </button>

            <div className="grid grid-cols-3 gap-8 max-w-2xl mx-auto">
              <div className="glass-card p-8 text-center">
                <div className="w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6" style={{ background: 'var(--gradient-primary)' }}>
                  <Bot className="h-8 w-8 text-white" />
                </div>
                <p className="text-white font-bold text-xl mb-2">AI Agents</p>
                <p className="text-white/60 text-lg">Intelligent Collaboration</p>
              </div>
              <div className="glass-card p-8 text-center">
                <div className="w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6" style={{ background: 'var(--gradient-secondary)' }}>
                  <Plus className="h-8 w-8 text-white" />
                </div>
                <p className="text-white font-bold text-xl mb-2">Real-time</p>
                <p className="text-white/60 text-lg">Live Updates</p>
              </div>
              <div className="glass-card p-8 text-center">
                <div className="w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6" style={{ background: 'var(--gradient-accent)' }}>
                  <Bot className="h-8 w-8 text-white" />
                </div>
                <p className="text-white font-bold text-xl mb-2">Research</p>
                <p className="text-white/60 text-lg">Deep Analysis</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col">
      <div className="flex-1 p-4">
        <div className="max-w-4xl mx-auto">
          <div className="space-y-4">
            {/* Chat messages will go here */}
            <div className="text-center text-muted-foreground">
              Chat interface for session: {chatId}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
