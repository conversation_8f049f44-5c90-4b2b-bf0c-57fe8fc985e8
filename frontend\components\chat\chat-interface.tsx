'use client'

import { Bo<PERSON>, Plus, FileText, Search } from 'lucide-react'

interface ChatInterfaceProps {
  chatId: string | null
  onNewChat: () => void
}

export function ChatInterface({ chatId, onNewChat }: ChatInterfaceProps) {
  if (!chatId) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-indigo-50 via-white to-cyan-50">
        <div className="max-w-4xl w-full text-center px-8">
          {/* Hero Section */}
          <div className="mb-12">
            <div className="flex justify-center mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-3xl flex items-center justify-center shadow-2xl">
                <Bot className="h-10 w-10 text-white" />
              </div>
            </div>
            <h1 className="text-5xl font-bold text-gray-900 mb-4">
              Welcome to <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">CrewAI</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
              Your intelligent research companion. Start a conversation and watch our AI agents collaborate to deliver comprehensive insights.
            </p>
          </div>

          {/* Action Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <button
              type="button"
              onClick={onNewChat}
              className="group p-6 bg-white rounded-2xl border border-indigo-100 hover:border-indigo-300 hover:shadow-xl transition-all duration-300 text-left transform hover:scale-105"
            >
              <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform shadow-lg">
                <Plus className="h-6 w-6 text-white" />
              </div>
              <div className="text-lg font-semibold text-gray-900 mb-2">Start Research</div>
              <div className="text-sm text-gray-600">Begin a new AI-powered research session</div>
            </button>

            <button
              type="button"
              className="group p-6 bg-white rounded-2xl border border-purple-100 hover:border-purple-300 hover:shadow-xl transition-all duration-300 text-left transform hover:scale-105"
            >
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform shadow-lg">
                <Bot className="h-6 w-6 text-white" />
              </div>
              <div className="text-lg font-semibold text-gray-900 mb-2">AI Agents</div>
              <div className="text-sm text-gray-600">Deploy specialized research agents</div>
            </button>

            <button
              type="button"
              className="group p-6 bg-white rounded-2xl border border-emerald-100 hover:border-emerald-300 hover:shadow-xl transition-all duration-300 text-left transform hover:scale-105"
            >
              <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform shadow-lg">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div className="text-lg font-semibold text-gray-900 mb-2">Analyze Data</div>
              <div className="text-sm text-gray-600">Upload and analyze your documents</div>
            </button>

            <button
              type="button"
              className="group p-6 bg-white rounded-2xl border border-orange-100 hover:border-orange-300 hover:shadow-xl transition-all duration-300 text-left transform hover:scale-105"
            >
              <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform shadow-lg">
                <Search className="h-6 w-6 text-white" />
              </div>
              <div className="text-lg font-semibold text-gray-900 mb-2">Generate Reports</div>
              <div className="text-sm text-gray-600">Create comprehensive research reports</div>
            </button>
          </div>

          {/* CTA Button */}
          <button
            type="button"
            onClick={onNewChat}
            className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-4 rounded-2xl font-semibold text-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-xl hover:shadow-2xl transform hover:scale-105"
          >
            Start Your First Research Session
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col bg-gradient-to-br from-indigo-50 via-white to-cyan-50">
      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto">
        <div className="max-w-4xl mx-auto px-6 py-8">
          {/* Welcome Message */}
          <div className="flex gap-4 mb-8">
            <div className="w-10 h-10 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg">
              <Bot className="h-5 w-5 text-white" />
            </div>
            <div className="flex-1">
              <div className="bg-white rounded-2xl rounded-tl-lg p-6 border border-indigo-100 shadow-sm">
                <div className="text-gray-900 leading-relaxed">
                  <p className="font-semibold text-lg mb-2 text-indigo-900">Welcome to your research session!</p>
                  <p className="text-gray-700">I'm your AI research assistant. I can help you:</p>
                  <ul className="list-disc list-inside mt-3 space-y-1 text-gray-600">
                    <li>Conduct comprehensive research on any topic</li>
                    <li>Analyze data and generate insights</li>
                    <li>Create detailed reports and summaries</li>
                    <li>Answer complex questions with citations</li>
                  </ul>
                  <p className="mt-4 text-indigo-700 font-medium">What would you like to research today?</p>
                </div>
              </div>
            </div>
          </div>

          {/* Sample conversation */}
          <div className="space-y-6">
            {/* User message example */}
            <div className="flex gap-4 justify-end">
              <div className="flex-1 max-w-2xl">
                <div className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-2xl rounded-tr-lg p-4 shadow-lg">
                  <p>Research the latest developments in artificial intelligence and machine learning for 2024</p>
                </div>
              </div>
              <div className="w-10 h-10 bg-gradient-to-r from-gray-200 to-gray-300 rounded-xl flex items-center justify-center flex-shrink-0">
                <div className="w-6 h-6 bg-gradient-to-r from-gray-400 to-gray-500 rounded-full"></div>
              </div>
            </div>

            {/* AI response example */}
            <div className="flex gap-4">
              <div className="w-10 h-10 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg">
                <Bot className="h-5 w-5 text-white" />
              </div>
              <div className="flex-1">
                <div className="bg-white rounded-2xl rounded-tl-lg p-6 border border-indigo-100 shadow-sm">
                  <div className="text-gray-900 leading-relaxed">
                    <p className="font-semibold mb-2 text-indigo-900">🔍 Research in Progress...</p>
                    <p className="text-gray-700 mb-4">I'm deploying specialized research agents to gather the latest information on AI and ML developments for 2024. This will include:</p>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
                        <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full animate-pulse"></div>
                        <span className="text-sm font-medium text-blue-800">Scanning academic papers and research publications</span>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-200">
                        <div className="w-3 h-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full animate-pulse"></div>
                        <span className="text-sm font-medium text-purple-800">Analyzing industry reports and trends</span>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-lg border border-emerald-200">
                        <div className="w-3 h-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full animate-pulse"></div>
                        <span className="text-sm font-medium text-emerald-800">Gathering insights from tech conferences and announcements</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Input Area */}
      <div className="border-t border-indigo-100 bg-white/80 backdrop-blur-sm p-6">
        <div className="max-w-4xl mx-auto">
          <div className="relative">
            <textarea
              placeholder="Ask me anything about research, data analysis, or any topic you'd like to explore..."
              className="w-full resize-none bg-white border border-indigo-200 rounded-2xl px-6 py-4 pr-14 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-gray-900 placeholder-gray-500 transition-all min-h-[56px] max-h-[200px] shadow-sm"
              rows={1}
            />
            <button
              type="button"
              title="Send message"
              className="absolute right-3 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl flex items-center justify-center hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              <Plus className="h-5 w-5" />
            </button>
          </div>
          <div className="text-xs text-gray-500 mt-3 text-center">
            CrewAI uses advanced AI agents to provide comprehensive research. Always verify important information.
          </div>
        </div>
      </div>
    </div>
  )
}
