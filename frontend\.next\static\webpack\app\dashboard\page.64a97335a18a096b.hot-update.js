"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/send.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Send; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst Send = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Send\", [\n  [\"path\", { d: \"m22 2-7 20-4-9-9-4Z\", key: \"1q3vgg\" }],\n  [\"path\", { d: \"M22 2 11 13\", key: \"nzbqef\" }]\n]);\n\n\n//# sourceMappingURL=send.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvc2VuZC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXRELGFBQWEsZ0VBQWdCO0FBQzdCLGFBQWEseUNBQXlDO0FBQ3RELGFBQWEsaUNBQWlDO0FBQzlDOztBQUUyQjtBQUMzQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL3NlbmQuanM/ZmNkZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC4yOTQuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IFNlbmQgPSBjcmVhdGVMdWNpZGVJY29uKFwiU2VuZFwiLCBbXG4gIFtcInBhdGhcIiwgeyBkOiBcIm0yMiAyLTcgMjAtNC05LTktNFpcIiwga2V5OiBcIjFxM3ZnZ1wiIH1dLFxuICBbXCJwYXRoXCIsIHsgZDogXCJNMjIgMiAxMSAxM1wiLCBrZXk6IFwibnpicWVmXCIgfV1cbl0pO1xuXG5leHBvcnQgeyBTZW5kIGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNlbmQuanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/chat/chat-interface.tsx":
/*!********************************************!*\
  !*** ./components/chat/chat-interface.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: function() { return /* binding */ ChatInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_FileText_Search_Send_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,FileText,Search,Send,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_FileText_Search_Send_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,FileText,Search,Send,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_FileText_Search_Send_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,FileText,Search,Send,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_FileText_Search_Send_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,FileText,Search,Send,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_FileText_Search_Send_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,FileText,Search,Send,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_FileText_Search_Send_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,FileText,Search,Send,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_FileText_Search_Send_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,FileText,Search,Send,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_FileText_Search_Send_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,FileText,Search,Send,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ChatInterface(param) {\n    let { chatId, onNewChat } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (chatId) {\n            loadMessages();\n        }\n    }, [\n        chatId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    const loadMessages = async ()=>{\n        if (!chatId) return;\n        setLoading(true);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.chatAPI.getMessages(chatId);\n            setMessages(response.data || []);\n        } catch (error) {\n            console.error(\"Error loading messages:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSend = async ()=>{\n        if (!input.trim() || sending) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            content: input.trim(),\n            role: \"user\",\n            created_at: new Date().toISOString()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInput(\"\");\n        setSending(true);\n        // Simulate AI response (replace with actual API call)\n        setTimeout(()=>{\n            const aiMessage = {\n                id: (Date.now() + 1).toString(),\n                content: \"I'm processing your request and will provide a comprehensive research analysis shortly...\",\n                role: \"assistant\",\n                created_at: new Date().toISOString()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiMessage\n                ]);\n            setSending(false);\n        }, 1000);\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    if (!chatId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex items-center justify-center bg-gradient-to-br from-violet-50 via-white to-pink-50 relative overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-cyan-400/20 rounded-full blur-3xl animate-pulse delay-1000\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl w-full text-center px-8 relative z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-24 h-24 bg-gradient-to-br from-violet-600 via-purple-600 to-pink-600 rounded-3xl flex items-center justify-center shadow-2xl transform rotate-3 hover:rotate-0 transition-transform duration-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_FileText_Search_Send_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"h-12 w-12 text-white animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full animate-bounce\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-6xl font-black text-gray-900 mb-6 leading-tight\",\n                                    children: [\n                                        \"Welcome to\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-violet-600 via-purple-600 to-pink-600 bg-clip-text text-transparent animate-pulse\",\n                                            children: \"CrewAI\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-light\",\n                                    children: [\n                                        \"Your intelligent research companion powered by collaborative AI agents.\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-purple-600 font-semibold\",\n                                            children: \" Start exploring limitless possibilities.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onNewChat,\n                                    className: \"group relative p-8 bg-white/80 backdrop-blur-sm rounded-3xl border border-violet-100 hover:border-violet-300 hover:shadow-2xl transition-all duration-500 text-left transform hover:scale-110 hover:-rotate-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-violet-500/10 to-purple-500/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-gradient-to-br from-violet-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform shadow-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_FileText_Search_Send_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-8 w-8 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl font-bold text-gray-900 mb-3\",\n                                                    children: \"Start Research\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Begin a new AI-powered research session\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onNewChat,\n                                    className: \"group relative p-8 bg-white/80 backdrop-blur-sm rounded-3xl border border-purple-100 hover:border-purple-300 hover:shadow-2xl transition-all duration-500 text-left transform hover:scale-110 hover:rotate-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform shadow-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_FileText_Search_Send_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-8 w-8 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl font-bold text-gray-900 mb-3\",\n                                                    children: \"AI Agents\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Deploy specialized research agents\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onNewChat,\n                                    className: \"group relative p-8 bg-white/80 backdrop-blur-sm rounded-3xl border border-emerald-100 hover:border-emerald-300 hover:shadow-2xl transition-all duration-500 text-left transform hover:scale-110 hover:-rotate-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-emerald-500/10 to-teal-500/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform shadow-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_FileText_Search_Send_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-8 w-8 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl font-bold text-gray-900 mb-3\",\n                                                    children: \"Analyze Data\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Upload and analyze your documents\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onNewChat,\n                                    className: \"group relative p-8 bg-white/80 backdrop-blur-sm rounded-3xl border border-orange-100 hover:border-orange-300 hover:shadow-2xl transition-all duration-500 text-left transform hover:scale-110 hover:rotate-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-orange-500/10 to-red-500/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform shadow-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_FileText_Search_Send_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-8 w-8 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl font-bold text-gray-900 mb-3\",\n                                                    children: \"Generate Reports\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Create comprehensive research reports\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: onNewChat,\n                            className: \"relative group bg-gradient-to-r from-violet-600 via-purple-600 to-pink-600 text-white px-12 py-6 rounded-2xl font-bold text-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-pink-600 via-purple-600 to-violet-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_FileText_Search_Send_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-6 w-6 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Start Your First Research Session\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_FileText_Search_Send_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-6 w-6 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 90,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col bg-gradient-to-br from-violet-50 via-white to-pink-50 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-20 right-20 w-64 h-64 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-3xl animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-20 left-20 w-64 h-64 bg-gradient-to-br from-blue-400/10 to-cyan-400/10 rounded-full blur-3xl animate-pulse delay-1000\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto px-6 py-8\",\n                    children: [\n                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 text-purple-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6 h-6 border-2 border-purple-600 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Loading messages...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this) : messages.length === 0 ? /* Welcome Message */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-4 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-gradient-to-br from-violet-600 to-purple-600 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_FileText_Search_Send_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-6 w-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/80 backdrop-blur-sm rounded-3xl rounded-tl-lg p-8 border border-violet-100 shadow-xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-900 leading-relaxed\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-bold text-2xl mb-4 bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent\",\n                                                    children: \"Welcome to your research session! ✨\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700 text-lg mb-4\",\n                                                    children: \"I'm your AI research assistant powered by collaborative agents. I can help you:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl border border-blue-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_FileText_Search_Send_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-blue-800\",\n                                                                    children: \"Conduct comprehensive research\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3 p-4 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-2xl border border-emerald-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_FileText_Search_Send_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-emerald-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                                    lineNumber: 238,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-emerald-800\",\n                                                                    children: \"Analyze data and insights\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                                    lineNumber: 239,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl border border-purple-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_FileText_Search_Send_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-purple-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                                    lineNumber: 242,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-purple-800\",\n                                                                    children: \"Create detailed reports\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                                    lineNumber: 243,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3 p-4 bg-gradient-to-r from-orange-50 to-red-50 rounded-2xl border border-orange-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_FileText_Search_Send_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-orange-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                                    lineNumber: 246,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-orange-800\",\n                                                                    children: \"Answer complex questions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                                    lineNumber: 247,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl font-semibold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent\",\n                                                    children: \"What would you like to research today? \\uD83D\\uDE80\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 13\n                        }, this) : /* Messages */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-4 \".concat(message.role === \"user\" ? \"justify-end\" : \"\"),\n                                        children: [\n                                            message.role === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-violet-600 to-purple-600 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-xl\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_FileText_Search_Send_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 max-w-3xl \".concat(message.role === \"user\" ? \"max-w-2xl\" : \"\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6 rounded-3xl shadow-lg \".concat(message.role === \"user\" ? \"bg-gradient-to-r from-violet-600 to-purple-600 text-white rounded-tr-lg\" : \"bg-white/80 backdrop-blur-sm border border-violet-100 rounded-tl-lg\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"leading-relaxed \".concat(message.role === \"user\" ? \"text-white\" : \"text-gray-900\"),\n                                                        children: message.content\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 19\n                                            }, this),\n                                            message.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-gray-200 to-gray-300 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-gradient-to-r from-gray-400 to-gray-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, message.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 17\n                                    }, this)),\n                                sending && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-br from-violet-600 to-purple-600 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_FileText_Search_Send_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-6 w-6 text-white animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-3xl rounded-tl-lg p-6 border border-violet-100 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 text-purple-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-4 h-4 border-2 border-purple-600 border-t-transparent rounded-full animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"AI is thinking...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-violet-100 bg-white/80 backdrop-blur-xl p-6 relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: input,\n                                    onChange: (e)=>setInput(e.target.value),\n                                    onKeyPress: handleKeyPress,\n                                    placeholder: \"Ask me anything about research, data analysis, or any topic you'd like to explore...\",\n                                    className: \"w-full resize-none bg-white/90 backdrop-blur-sm border-2 border-violet-200 rounded-3xl px-6 py-4 pr-16 focus:outline-none focus:ring-4 focus:ring-violet-500/20 focus:border-violet-500 text-gray-900 placeholder-gray-500 transition-all min-h-[60px] max-h-[200px] shadow-xl\",\n                                    rows: 1,\n                                    disabled: sending\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleSend,\n                                    disabled: !input.trim() || sending,\n                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-gradient-to-r from-violet-600 to-purple-600 text-white rounded-2xl flex items-center justify-center hover:from-violet-700 hover:to-purple-700 transition-all duration-200 shadow-xl hover:shadow-2xl disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105\",\n                                    children: sending ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_FileText_Search_Send_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500 mt-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent font-semibold\",\n                                    children: \"CrewAI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this),\n                                \" uses advanced AI agents to provide comprehensive research. Always verify important information.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 308,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 307,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 202,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"+XuFDXxH1AuiYSRQ2Vw93MpMt7A=\");\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/chat/chat-interface.tsx\n"));

/***/ })

});