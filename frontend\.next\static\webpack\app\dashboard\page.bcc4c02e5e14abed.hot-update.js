"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/chat/chat-history.tsx":
/*!******************************************!*\
  !*** ./components/chat/chat-history.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatHistory: function() { return /* binding */ ChatHistory; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ ChatHistory auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ChatHistory(param) {\n    let { selectedChatId, onChatSelect } = param;\n    _s();\n    const [chats, setChats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchChats = async ()=>{\n            setLoading(true);\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.chatAPI.getHistory();\n                setChats(response.data);\n            } catch (error) {\n                console.error(\"Error fetching chat history:\", error);\n            // TODO: Handle error (e.g., show error message)\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchChats();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: [\n                1,\n                2,\n                3\n            ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-16 bg-gray-100 animate-pulse rounded-xl\"\n                }, i, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this);\n    }\n    if (chats.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-8 w-8 text-gray-300 mx-auto mb-3\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-500 font-medium\",\n                    children: \"No conversations yet\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-gray-400 mt-1\",\n                    children: \"Start a new research session to begin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2\",\n        children: chats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                type: \"button\",\n                className: \"w-full text-left p-4 rounded-xl transition-all duration-200 group \".concat(selectedChatId === chat.id ? \"bg-blue-50 border border-blue-200 shadow-sm\" : \"hover:bg-gray-50 border border-transparent\"),\n                onClick: ()=>onChatSelect(chat.id),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 rounded-full mt-2 \".concat(selectedChatId === chat.id ? \"bg-blue-500\" : \"bg-gray-300\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"truncate text-sm font-medium \".concat(selectedChatId === chat.id ? \"text-blue-900\" : \"text-gray-900\"),\n                                    children: chat.topic\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mt-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs \".concat(selectedChatId === chat.id ? \"text-blue-600\" : \"text-gray-500\"),\n                                            children: new Date(chat.created_at).toLocaleDateString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-2 py-0.5 rounded-full text-xs font-medium \".concat(chat.status === \"completed\" ? \"bg-green-100 text-green-700\" : chat.status === \"running\" ? \"bg-blue-100 text-blue-700\" : chat.status === \"failed\" ? \"bg-red-100 text-red-700\" : \"bg-gray-100 text-gray-600\"),\n                                            children: chat.status\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 11\n                }, this)\n            }, chat.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatHistory, \"RtCxTJDryflVzjLVpr7zS1FVD/A=\");\n_c = ChatHistory;\nvar _c;\n$RefreshReg$(_c, \"ChatHistory\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/chat/chat-history.tsx\n"));

/***/ })

});