#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create the first admin user for the Gemini CrewAI application.
Run this script after setting up the database to create an admin account.
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from sqlalchemy.orm import Session
from database import SessionLocal, engine
from models.models import Base, User
from services.auth_service import get_password_hash
import uuid
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def create_admin_user():
    """Create the first admin user"""
    
    # Create all database tables
    print("Creating database tables...")
    Base.metadata.create_all(bind=engine)
    print("✓ Database tables created successfully!")
    
    # Create database session
    db = SessionLocal()
    
    try:
        # Check if admin user already exists
        existing_admin = db.query(User).filter(User.email == "<EMAIL>").first()
        if existing_admin:
            print("❌ Admin user already exists!")
            print("Email: <EMAIL>")
            return
        
        # Create admin user
        print("Creating admin user...")
        admin_user = User(
            id=str(uuid.uuid4()),
            email="<EMAIL>",
            username="admin",
            hashed_password=get_password_hash("admin123"),
            full_name="System Administrator",
            is_admin=True,
            is_active=True
        )
        
        db.add(admin_user)
        db.commit()
        
        print("✓ Admin user created successfully!")
        print("\n" + "="*50)
        print("ADMIN LOGIN CREDENTIALS")
        print("="*50)
        print("Email:    <EMAIL>")
        print("Password: admin123")
        print("="*50)
        print("\n⚠️  IMPORTANT: Please change the password after first login!")
        print("⚠️  You can create additional users through the admin panel.")
        
    except Exception as e:
        print(f"❌ Error creating admin user: {e}")
        db.rollback()
    finally:
        db.close()

def create_sample_user():
    """Create a sample regular user for testing"""
    db = SessionLocal()
    
    try:
        # Check if sample user already exists
        existing_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if existing_user:
            print("Sample user already exists!")
            return
        
        # Create sample user
        print("Creating sample user...")
        sample_user = User(
            id=str(uuid.uuid4()),
            email="<EMAIL>",
            username="testuser",
            hashed_password=get_password_hash("user123"),
            full_name="Test User",
            is_admin=False,
            is_active=True
        )
        
        db.add(sample_user)
        db.commit()
        
        print("✓ Sample user created successfully!")
        print("Email: <EMAIL>")
        print("Password: user123")
        
    except Exception as e:
        print(f"❌ Error creating sample user: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("Gemini CrewAI - Admin User Setup")
    print("="*40)
    
    # Check if database URL is set
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        print("❌ DATABASE_URL environment variable is not set!")
        print("Please create a .env file with your Neon database connection string.")
        sys.exit(1)
    
    print(f"Database URL: {database_url[:50]}...")
    
    try:
        # Create admin user
        create_admin_user()
        
        # Ask if user wants to create a sample user
        create_sample = input("\nDo you want to create a sample regular user? (y/N): ").lower().strip()
        if create_sample in ['y', 'yes']:
            create_sample_user()
        
        print("\n✓ Setup completed successfully!")
        print("\nNext steps:")
        print("1. Start the backend server: uvicorn main:app --reload")
        print("2. Start the frontend: npm run dev")
        print("3. Visit http://localhost:3000 to login")
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        sys.exit(1)
