[project]
name = "gemini"
version = "0.1.0"
description = "gemini using crewAI"
authors = [{ name = "Your Name", email = "<EMAIL>" }]
requires-python = ">=3.10,<3.13"
dependencies = [
    "crewai[tools]>=0.108.0,<1.0.0"
]

[project.scripts]
gemini = "gemini.main:run"
run_crew = "gemini.main:run"
train = "gemini.main:train"
replay = "gemini.main:replay"
test = "gemini.main:test"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.crewai]
type = "crew"
