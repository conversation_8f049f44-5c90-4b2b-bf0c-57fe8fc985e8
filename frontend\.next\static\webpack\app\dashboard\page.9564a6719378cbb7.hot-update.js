"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/chat/chat-history.tsx":
/*!******************************************!*\
  !*** ./components/chat/chat-history.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatHistory: function() { return /* binding */ ChatHistory; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ ChatHistory auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ChatHistory(param) {\n    let { selectedChatId, onChatSelect, onChatDeleted } = param;\n    _s();\n    const [chats, setChats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showClearConfirm, setShowClearConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deletingChatId, setDeletingChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchChats();\n    }, []);\n    const fetchChats = async ()=>{\n        setLoading(true);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.chatAPI.getHistory();\n            setChats(response.data);\n        } catch (error) {\n            console.error(\"Error fetching chat history:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDeleteChat = async (chatId, e)=>{\n        e.stopPropagation();\n        setDeletingChatId(chatId);\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.chatAPI.deleteChat(chatId);\n            setChats((prev)=>prev.filter((chat)=>chat.id !== chatId));\n            onChatDeleted === null || onChatDeleted === void 0 ? void 0 : onChatDeleted(chatId);\n        } catch (error) {\n            console.error(\"Error deleting chat:\", error);\n        } finally{\n            setDeletingChatId(null);\n        }\n    };\n    const handleClearAll = async ()=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.chatAPI.clearAllChats();\n            setChats([]);\n            setShowClearConfirm(false);\n            if (selectedChatId) {\n                onChatDeleted === null || onChatDeleted === void 0 ? void 0 : onChatDeleted(selectedChatId);\n            }\n        } catch (error) {\n            console.error(\"Error clearing all chats:\", error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: [\n                1,\n                2,\n                3\n            ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-14 bg-[#1a1a1a] animate-pulse rounded-xl border border-[#222222]\"\n                }, i, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this);\n    }\n    if (chats.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-12 h-12 bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl flex items-center justify-center mx-auto mb-4 border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-6 w-6 text-gray-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm font-medium text-gray-400 mb-1\",\n                    children: \"No conversations yet\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-gray-600\",\n                    children: \"Start a new research session to begin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2\",\n        children: chats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                type: \"button\",\n                className: \"chat-item \".concat(selectedChatId === chat.id ? \"active\" : \"\"),\n                onClick: ()=>onChatSelect(chat.id),\n                children: [\n                    selectedChatId === chat.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-start gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full mt-2 transition-all duration-300 \".concat(selectedChatId === chat.id ? \"bg-gradient-to-r from-blue-400 to-purple-400 shadow-lg shadow-blue-400/50\" : \"bg-gray-600 group-hover:bg-gray-500\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"truncate text-sm font-medium transition-colors \".concat(selectedChatId === chat.id ? \"text-white\" : \"text-gray-300 group-hover:text-white\"),\n                                        children: chat.topic\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mt-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs transition-colors \".concat(selectedChatId === chat.id ? \"text-blue-300\" : \"text-gray-500 group-hover:text-gray-400\"),\n                                                children: new Date(chat.created_at).toLocaleDateString()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-2 py-0.5 rounded-full text-xs font-medium transition-all \".concat(chat.status === \"completed\" ? \"bg-emerald-500/20 text-emerald-400 border border-emerald-500/30\" : chat.status === \"running\" ? \"bg-blue-500/20 text-blue-400 border border-blue-500/30\" : chat.status === \"failed\" ? \"bg-red-500/20 text-red-400 border border-red-500/30\" : \"bg-gray-500/20 text-gray-400 border border-gray-500/30\"),\n                                                children: chat.status\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, chat.id, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatHistory, \"Fa+ojphl57YKEmTpKLKhSGoO/BM=\");\n_c = ChatHistory;\nvar _c;\n$RefreshReg$(_c, \"ChatHistory\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/chat/chat-history.tsx\n"));

/***/ })

});