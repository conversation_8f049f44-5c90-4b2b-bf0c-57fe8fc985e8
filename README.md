# 🤖 Gemini CrewAI - Modern UI Platform

A beautiful, modern web interface for your CrewAI agents with real-time updates, file management, and admin controls.

![Gemini CrewAI](https://img.shields.io/badge/CrewAI-Modern%20UI-blue)
![Python](https://img.shields.io/badge/Python-3.10+-green)
![Next.js](https://img.shields.io/badge/Next.js-14-black)
![TypeScript](https://img.shields.io/badge/TypeScript-5-blue)

## ✨ Features

### 🔐 Authentication & Security
- **Admin-only user management** (no public signup)
- **JWT-based authentication** with secure token handling
- **Role-based access control** (admin vs regular users)

### 💬 Modern Chat Interface
- **Real-time agent updates** via WebSocket
- **Collapsible chat history** sidebar
- **Progress tracking** with live status updates
- **Beautiful message bubbles** with agent identification

### 📁 File Management
- **Collapsible file viewer** sidebar
- **Automatic file detection** from agent outputs
- **File preview** with syntax highlighting
- **Download functionality** for generated reports

### 🎨 Beautiful UI/UX
- **Modern design** with Tailwind CSS
- **Responsive layout** for all screen sizes
- **Dark/light theme** support
- **Smooth animations** and transitions
- **Professional color scheme**

### 🔄 Real-time Updates
- **Live progress bars** during agent execution
- **Instant message updates** from agents
- **File generation notifications**
- **Connection status indicators**

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Database      │
│   (Next.js)     │◄──►│   (FastAPI)     │◄──►│   (Neon PG)     │
│                 │    │                 │    │                 │
│ • React 18      │    │ • Python 3.10+ │    │ • PostgreSQL    │
│ • TypeScript    │    │ • SQLAlchemy    │    │ • Cloud hosted  │
│ • Tailwind CSS  │    │ • WebSockets    │    │ • Auto-scaling  │
│ • Zustand       │    │ • JWT Auth      │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   CrewAI Core   │
                       │                 │
                       │ • Your Agents   │
                       │ • Existing Code │
                       │ • Tools & Tasks │
                       └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Python 3.10+
- Node.js 18+
- Neon Database account
- API keys (OpenAI, Serper, etc.)

### 1. Clone & Setup
```bash
git clone <your-repo>
cd gemini
```

### 2. Database Setup
1. Create a [Neon](https://neon.tech) database
2. Copy your connection string

### 3. Backend Setup
```bash
cd backend
python -m venv venv

# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate

pip install -r requirements.txt
cp .env.example .env
# Edit .env with your database URL and API keys
```

### 4. Frontend Setup
```bash
cd frontend
npm install
echo "NEXT_PUBLIC_API_URL=http://localhost:8000" > .env.local
echo "NEXT_PUBLIC_WS_URL=ws://localhost:8000" >> .env.local
```

### 5. Create Admin User
```bash
cd backend
python create_admin.py
```

### 6. Start Everything
```bash
# Option 1: Use startup scripts
# Windows:
start.bat

# macOS/Linux:
chmod +x start.sh
./start.sh

# Option 2: Manual start
# Terminal 1 - Backend:
cd backend && uvicorn main:app --reload

# Terminal 2 - Frontend:
cd frontend && npm run dev
```

### 7. Access the App
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs

**Default Login:**
- Email: `<EMAIL>`
- Password: `admin123`

## 📱 Usage

### For Admins
1. **Login** with admin credentials
2. **Create users** through the admin panel
3. **Manage user permissions** and access
4. **Monitor system usage**

### For Users
1. **Login** with provided credentials
2. **Start new research** by clicking "New Research"
3. **Enter your topic** and watch agents work in real-time
4. **View progress** in the main chat area
5. **Access files** in the right sidebar
6. **Browse history** in the left sidebar

## 🛠️ Development

### Project Structure
```
gemini/
├── backend/                 # FastAPI backend
│   ├── main.py             # Main application
│   ├── database.py         # Database config
│   ├── models/             # SQLAlchemy models
│   ├── api/                # API routes
│   ├── services/           # Business logic
│   └── requirements.txt    # Dependencies
├── frontend/               # Next.js frontend
│   ├── app/                # App router pages
│   ├── components/         # React components
│   ├── lib/                # Utils & stores
│   └── package.json        # Dependencies
├── src/gemini/            # Your CrewAI code
└── setup.md               # Detailed setup guide
```

### Key Technologies
- **Backend**: FastAPI, SQLAlchemy, WebSockets, JWT
- **Frontend**: Next.js 14, TypeScript, Tailwind CSS, Zustand
- **Database**: PostgreSQL (Neon)
- **UI Components**: Shadcn/ui, Radix UI
- **Real-time**: WebSocket connections

### Adding New Features
1. **New Agents**: Update YAML configs and crew.py
2. **UI Changes**: Modify components in frontend/components/
3. **API Endpoints**: Add routes in backend/api/
4. **Database Changes**: Update models in backend/models/

## 🔧 Configuration

### Environment Variables

**Backend (.env)**
```env
DATABASE_URL=******************************
SECRET_KEY=your-secret-key
OPENAI_API_KEY=your-openai-key
SERPER_API_KEY=your-serper-key
```

**Frontend (.env.local)**
```env
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000
```

## 📊 Features in Detail

### Real-time Agent Execution
- Live progress tracking
- Agent-specific message colors
- Error handling and display
- Completion notifications

### File Management
- Automatic file detection
- Preview with syntax highlighting
- Download functionality
- File type icons

### User Management
- Admin-controlled user creation
- Role-based permissions
- User activity tracking
- Account management

## 🚀 Deployment

### Production Setup
1. **Environment**: Set production environment variables
2. **Database**: Use production Neon database
3. **Security**: Update secret keys and CORS settings
4. **SSL**: Configure HTTPS certificates
5. **Monitoring**: Add logging and error tracking

### Recommended Hosting
- **Frontend**: Vercel, Netlify
- **Backend**: Railway, Render, DigitalOcean
- **Database**: Neon (already cloud-hosted)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

- **Setup Issues**: Check `setup.md` for detailed instructions
- **API Documentation**: Visit http://localhost:8000/docs
- **Frontend Issues**: Check browser console for errors
- **Backend Issues**: Check terminal logs

## 🎯 Roadmap

- [ ] Advanced user permissions
- [ ] Agent configuration UI
- [ ] Analytics dashboard
- [ ] File upload functionality
- [ ] Mobile app
- [ ] API rate limiting
- [ ] Advanced monitoring

---

**Built with ❤️ for the CrewAI community**
