import sys
import os
import asyncio
from datetime import datetime
from sqlalchemy.orm import Session
from typing import Dict, Any
import traceback

# Add the gemini source to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '../../gemini/src'))

from gemini.crew import Gemini
from services.websocket_manager import WebSocketManager
from models.models import Chat, Message, File, AgentExecution

class CrewService:
    def __init__(self, websocket_manager: WebSocketManager, user_id: str, chat_id: str):
        self.websocket_manager = websocket_manager
        self.user_id = user_id
        self.chat_id = chat_id
        self.crew = Gemini()
    
    async def execute_crew(self, topic: str, db: Session):
        """Execute the CrewAI crew with real-time updates"""
        try:
            # Update chat status
            chat = db.query(Chat).filter(Chat.id == self.chat_id).first()
            chat.status = "running"
            db.commit()
            
            # Send initial progress update
            await self.websocket_manager.send_progress_update(
                self.user_id, self.chat_id, 0, "starting", "system"
            )
            
            # Prepare inputs
            inputs = {
                'topic': topic,
                'current_year': str(datetime.now().year)
            }
            
            # Send agent start notification
            await self.websocket_manager.send_agent_update(
                self.user_id, self.chat_id, "system", 
                f"Starting research on: {topic}", "info"
            )
            
            # Execute crew with custom callbacks
            result = await self._execute_with_callbacks(inputs, db)
            
            # Update chat status to completed
            chat.status = "completed"
            chat.completed_at = datetime.utcnow()
            db.commit()
            
            # Send completion update
            await self.websocket_manager.send_completion_update(
                self.user_id, self.chat_id, True, str(result)
            )
            
            # Check for generated files
            await self._check_and_store_files(db)
            
        except Exception as e:
            # Update chat status to failed
            chat = db.query(Chat).filter(Chat.id == self.chat_id).first()
            chat.status = "failed"
            db.commit()
            
            # Log error message
            error_message = Message(
                chat_id=self.chat_id,
                agent_name="system",
                message_type="error",
                content=f"Error executing crew: {str(e)}"
            )
            db.add(error_message)
            db.commit()
            
            # Send error update
            await self.websocket_manager.send_completion_update(
                self.user_id, self.chat_id, False, error=str(e)
            )
            
            print(f"Error in crew execution: {e}")
            traceback.print_exc()
    
    async def _execute_with_callbacks(self, inputs: Dict[str, Any], db: Session):
        """Execute crew with progress callbacks"""
        # This is a simplified version - you might need to modify the CrewAI code
        # to add proper callbacks for real-time updates
        
        # Simulate agent progress updates
        agents = ["information_retrieval_specialist", "research_analyst", "research_synthesizer"]
        
        for i, agent_name in enumerate(agents):
            progress = int((i / len(agents)) * 100)
            
            # Send progress update
            await self.websocket_manager.send_progress_update(
                self.user_id, self.chat_id, progress, "running", agent_name
            )
            
            # Send agent start message
            await self.websocket_manager.send_agent_update(
                self.user_id, self.chat_id, agent_name, 
                f"Starting {agent_name.replace('_', ' ').title()}", "info"
            )
            
            # Store agent execution start
            agent_execution = AgentExecution(
                chat_id=self.chat_id,
                agent_name=agent_name,
                task_name=f"{agent_name}_task",
                status="running",
                start_time=datetime.utcnow()
            )
            db.add(agent_execution)
            db.commit()
        
        # Execute the actual crew
        # Note: This runs synchronously - you might want to modify CrewAI for async support
        result = await asyncio.get_event_loop().run_in_executor(
            None, lambda: self.crew.crew().kickoff(inputs=inputs)
        )
        
        # Update final progress
        await self.websocket_manager.send_progress_update(
            self.user_id, self.chat_id, 100, "completed"
        )
        
        return result
    
    async def _check_and_store_files(self, db: Session):
        """Check for generated files and store them in database"""
        # Check for the report file
        report_path = os.path.join(os.path.dirname(__file__), '../../gemini/knowledge/report.md')
        
        if os.path.exists(report_path):
            try:
                with open(report_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Store file in database
                file_record = File(
                    chat_id=self.chat_id,
                    filename="report.md",
                    file_path=report_path,
                    file_type="md",
                    file_size=len(content.encode('utf-8')),
                    content=content
                )
                db.add(file_record)
                db.commit()
                
                # Send file update
                await self.websocket_manager.send_file_update(
                    self.user_id, self.chat_id, {
                        "id": file_record.id,
                        "filename": file_record.filename,
                        "file_type": file_record.file_type,
                        "file_size": file_record.file_size,
                        "created_at": file_record.created_at.isoformat()
                    }
                )
                
            except Exception as e:
                print(f"Error storing file: {e}")
