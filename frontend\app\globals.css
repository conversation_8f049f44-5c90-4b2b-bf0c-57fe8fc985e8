@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Geist:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Beautiful Light Mode Color Palette */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;

  --secondary-50: #fdf2f8;
  --secondary-100: #fce7f3;
  --secondary-200: #fbcfe8;
  --secondary-300: #f9a8d4;
  --secondary-400: #f472b6;
  --secondary-500: #ec4899;
  --secondary-600: #db2777;
  --secondary-700: #be185d;
  --secondary-800: #9d174d;
  --secondary-900: #831843;

  --accent-50: #fefce8;
  --accent-100: #fef9c3;
  --accent-200: #fef08a;
  --accent-300: #fde047;
  --accent-400: #facc15;
  --accent-500: #eab308;
  --accent-600: #ca8a04;
  --accent-700: #a16207;
  --accent-800: #854d0e;
  --accent-900: #713f12;

  --neutral-50: #ffffff;
  --neutral-100: #f8fafc;
  --neutral-200: #f1f5f9;
  --neutral-300: #e2e8f0;
  --neutral-400: #cbd5e1;
  --neutral-500: #94a3b8;
  --neutral-600: #64748b;
  --neutral-700: #475569;
  --neutral-800: #334155;
  --neutral-900: #1e293b;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);

  /* Beautiful Light Mode Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-400), var(--primary-600));
  --gradient-secondary: linear-gradient(135deg, var(--secondary-400), var(--secondary-600));
  --gradient-accent: linear-gradient(135deg, var(--accent-400), var(--accent-600));
  --gradient-hero: linear-gradient(135deg, var(--primary-50), var(--secondary-50), var(--accent-50));
  --gradient-card: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
  --gradient-button: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  --gradient-text: linear-gradient(135deg, var(--primary-600), var(--secondary-600), var(--accent-600));
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-feature-settings: "cv02", "cv03", "cv04", "cv11";
}

body {
  font-family: 'Geist', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-weight: 400;
  line-height: 1.5;
  color: var(--neutral-900);
  background: linear-gradient(135deg, #fafbfc 0%, #f8fafc 50%, #f1f5f9 100%);
  min-height: 100vh;
  overflow-x: hidden;
  font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  @apply bg-muted;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-full;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50;
}

/* Agent message animations */
.agent-message {
  @apply animate-fade-in;
}

/* Progress bar animation */
.progress-bar {
  transition: width 0.3s ease-in-out;
}

/* Chat bubble styles */
.chat-bubble {
  @apply rounded-lg p-4 mb-4 border;
}

.chat-bubble.user {
  @apply bg-primary text-primary-foreground ml-8;
}

.chat-bubble.agent {
  @apply bg-muted mr-8;
}

.chat-bubble.system {
  @apply bg-accent text-accent-foreground;
}

.chat-bubble.error {
  @apply bg-destructive text-destructive-foreground;
}

/* File preview styles */
.file-preview {
  @apply border rounded-lg p-4 bg-card;
}

.file-preview pre {
  @apply bg-muted p-4 rounded overflow-x-auto text-sm;
}

/* Sidebar animations */
.sidebar-enter {
  @apply animate-slide-in;
}

/* Loading spinner */
.loading-spinner {
  @apply animate-spin rounded-full border-2 border-muted border-t-primary;
}

/* Blob animation for login page */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-1000 {
  animation-delay: 1s;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-3000 {
  animation-delay: 3s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Ultra-Modern 2024 Components */
.neo-card {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(24px);
  backdrop-filter: blur(24px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.08),
    0 16px 32px rgba(0, 0, 0, 0.04),
    0 8px 16px rgba(0, 0, 0, 0.02),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.neo-card:hover {
  transform: translateY(-4px);
  box-shadow:
    0 48px 96px rgba(0, 0, 0, 0.12),
    0 24px 48px rgba(0, 0, 0, 0.06),
    0 12px 24px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.neo-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(56, 189, 248, 0.6), transparent);
}

.neo-button {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #ec4899 100%);
  border: none;
  border-radius: 16px;
  color: white;
  font-weight: 700;
  font-size: 16px;
  padding: 16px 32px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow:
    0 8px 32px rgba(59, 130, 246, 0.25),
    0 4px 16px rgba(139, 92, 246, 0.15),
    0 2px 8px rgba(236, 72, 153, 0.1);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-decoration: none;
  user-select: none;
  -webkit-user-select: none;
}

.neo-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.neo-button:hover::before {
  left: 100%;
}

.neo-button:hover {
  transform: translateY(-2px);
  box-shadow:
    0 16px 48px rgba(59, 130, 246, 0.35),
    0 8px 24px rgba(139, 92, 246, 0.25),
    0 4px 12px rgba(236, 72, 153, 0.15);
}

.neo-button:active {
  transform: translateY(0px);
  transition: transform 0.1s ease;
}

.neo-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.neo-button:disabled:hover {
  transform: none;
  box-shadow:
    0 8px 32px rgba(59, 130, 246, 0.25),
    0 4px 16px rgba(139, 92, 246, 0.15),
    0 2px 8px rgba(236, 72, 153, 0.1);
}

.modern-input {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.6);
  border-radius: 20px;
  color: var(--neutral-800);
  padding: 18px 24px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.modern-input:focus {
  border-color: var(--primary-400);
  box-shadow:
    0 0 0 4px rgba(56, 189, 248, 0.15),
    0 8px 24px rgba(0, 0, 0, 0.1);
  outline: none;
  transform: translateY(-1px);
}

.modern-input::placeholder {
  color: var(--neutral-500);
  font-weight: 400;
}

.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradient-shift 3s ease-in-out infinite;
}

.light-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.3;
  animation: float-light 8s ease-in-out infinite;
}

.light-orb:nth-child(1) {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, var(--primary-200), var(--primary-100));
  top: 5%;
  left: 5%;
  animation-delay: 0s;
}

.light-orb:nth-child(2) {
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, var(--secondary-200), var(--secondary-100));
  top: 50%;
  right: 5%;
  animation-delay: 3s;
}

.light-orb:nth-child(3) {
  width: 350px;
  height: 350px;
  background: radial-gradient(circle, var(--accent-200), var(--accent-100));
  bottom: 5%;
  left: 40%;
  animation-delay: 6s;
}

.decorative-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.shape {
  position: absolute;
  opacity: 0.1;
  animation: rotate-slow 20s linear infinite;
}

.shape-1 {
  top: 10%;
  left: 80%;
  width: 60px;
  height: 60px;
  background: var(--gradient-primary);
  border-radius: 12px;
  transform: rotate(45deg);
}

.shape-2 {
  top: 70%;
  left: 10%;
  width: 40px;
  height: 40px;
  background: var(--gradient-secondary);
  border-radius: 50%;
}

.shape-3 {
  top: 30%;
  right: 20%;
  width: 80px;
  height: 20px;
  background: var(--gradient-accent);
  border-radius: 10px;
  transform: rotate(30deg);
}

.sparkle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: white;
  border-radius: 50%;
  animation: sparkle 2s ease-in-out infinite;
}

.sparkle:nth-child(odd) {
  animation-delay: 1s;
}

/* Beautiful Animations */
@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-30px) rotate(120deg); }
  66% { transform: translateY(15px) rotate(240deg); }
}

@keyframes sparkle {
  0%, 100% { opacity: 0; transform: scale(0); }
  50% { opacity: 1; transform: scale(1); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 20px rgba(14, 165, 233, 0.3); }
  50% { box-shadow: 0 0 40px rgba(14, 165, 233, 0.6), 0 0 60px rgba(217, 70, 239, 0.3); }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes float-light {
  0%, 100% { transform: translateY(0px) translateX(0px) scale(1); }
  25% { transform: translateY(-20px) translateX(10px) scale(1.05); }
  50% { transform: translateY(10px) translateX(-15px) scale(0.95); }
  75% { transform: translateY(-15px) translateX(5px) scale(1.02); }
}

@keyframes rotate-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes bounce-gentle {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Utility Classes */
.animate-slide-up {
  animation: slide-up 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-bounce-gentle {
  animation: bounce-gentle 3s ease-in-out infinite;
}

.animate-float-light {
  animation: float-light 8s ease-in-out infinite;
}
