'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { FileText, Download, Eye } from 'lucide-react'
import { filesAPI } from '@/lib/api'

interface FileInfo {
  id: string
  filename: string
  file_type: string
  file_size: number
  created_at: string
}

interface FileViewerProps {
  chatId: string | null
}

export function FileViewer({ chatId }: FileViewerProps) {
  const [files, setFiles] = useState<FileInfo[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    const fetchFiles = async () => {
      if (chatId) {
        setLoading(true);
        try {
          const response = await filesAPI.getChatFiles(chatId);
          setFiles(response.data);
        } catch (error) {
          console.error('Error fetching files:', error);
          // TODO: Handle error (e.g., show error message)
        } finally {
          setLoading(false);
        }
      }
    };

    fetchFiles();
  }, [chatId]);

  if (!chatId) {
    return (
      <div className="p-4 text-center">
        <FileText className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
        <p className="text-sm text-muted-foreground">No chat selected</p>
        <p className="text-xs text-muted-foreground mt-1">
          Select a chat to view generated files
        </p>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="p-4">
        <div className="space-y-2">
          {[1, 2].map((i) => (
            <div key={i} className="h-16 bg-muted animate-pulse rounded-lg" />
          ))}
        </div>
      </div>
    )
  }

  if (files.length === 0) {
    return (
      <div className="p-4 text-center">
        <FileText className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
        <p className="text-sm text-muted-foreground">No files generated yet</p>
        <p className="text-xs text-muted-foreground mt-1">
          Files will appear here when agents complete their work
        </p>
      </div>
    )
  }

  return (
    <div className="p-4 space-y-2">
      {files.map((file) => (
        <Card key={file.id} className="p-3">
          <div className="space-y-2">
            <div className="flex items-start gap-2">
              <FileText className="h-4 w-4 text-muted-foreground mt-0.5" />
              <div className="flex-1 min-w-0">
                <div className="font-medium text-sm truncate">{file.filename}</div>
                <div className="text-xs text-muted-foreground">
                  {(file.file_size / 1024).toFixed(1)} KB • {file.file_type.toUpperCase()}
                </div>
              </div>
            </div>
            <div className="flex gap-1">
              <Button size="sm" variant="outline" className="flex-1">
                <Eye className="h-3 w-3 mr-1" />
                View
              </Button>
              <Button size="sm" variant="outline" className="flex-1">
                <Download className="h-3 w-3 mr-1" />
                Download
              </Button>
            </div>
          </div>
        </Card>
      ))}
    </div>
  )
}
