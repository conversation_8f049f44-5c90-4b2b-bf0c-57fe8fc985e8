"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/chat/chat-history.tsx":
/*!******************************************!*\
  !*** ./components/chat/chat-history.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatHistory: function() { return /* binding */ ChatHistory; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ ChatHistory auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ChatHistory(param) {\n    let { selectedChatId, onChatSelect } = param;\n    _s();\n    const [chats, setChats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchChats = async ()=>{\n            setLoading(true);\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.chatAPI.getHistory();\n                setChats(response.data);\n            } catch (error) {\n                console.error(\"Error fetching chat history:\", error);\n            // TODO: Handle error (e.g., show error message)\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchChats();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: [\n                1,\n                2,\n                3\n            ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-12 bg-zinc-800/50 animate-pulse rounded-lg mx-3\"\n                }, i, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this);\n    }\n    if (chats.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8 px-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-6 w-6 text-zinc-500 mx-auto mb-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-zinc-400\",\n                    children: \"No conversations yet\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-1\",\n        children: chats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                type: \"button\",\n                className: \"w-full text-left p-3 mx-3 rounded-lg transition-colors group \".concat(selectedChatId === chat.id ? \"bg-zinc-800 text-white\" : \"hover:bg-zinc-800/50 text-zinc-300\"),\n                onClick: ()=>onChatSelect(chat.id),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"truncate text-sm font-medium\",\n                        children: chat.topic\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-zinc-500 mt-1\",\n                        children: new Date(chat.created_at).toLocaleDateString()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, chat.id, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\components\\\\chat\\\\chat-history.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatHistory, \"RtCxTJDryflVzjLVpr7zS1FVD/A=\");\n_c = ChatHistory;\nvar _c;\n$RefreshReg$(_c, \"ChatHistory\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/chat/chat-history.tsx\n"));

/***/ })

});