'use client'

import { useState, useEffect } from 'react'
import { MessageSquare } from 'lucide-react'
import { chatAPI } from '@/lib/api'

interface Chat {
  id: string
  topic: string
  status: string
  created_at: string
}

interface ChatHistoryProps {
  selectedChatId: string | null
  onChatSelect: (chatId: string) => void
}

export function ChatHistory({ selectedChatId, onChatSelect }: ChatHistoryProps) {
  const [chats, setChats] = useState<Chat[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchChats = async () => {
      setLoading(true)
      try {
        const response = await chatAPI.getHistory()
        setChats(response.data)
      } catch (error) {
        console.error('Error fetching chat history:', error)
        // TODO: Handle error (e.g., show error message)
      } finally {
        setLoading(false)
      }
    }

    fetchChats()
  }, [])

  if (loading) {
    return (
      <div className="space-y-2">
        {[1, 2, 3].map((i) => (
          <div key={i} className="h-12 bg-gray-200 animate-pulse rounded-md" />
        ))}
      </div>
    )
  }

  if (chats.length === 0) {
    return (
      <div className="text-center py-8">
        <MessageSquare className="h-6 w-6 text-gray-400 mx-auto mb-2" />
        <p className="text-sm text-gray-600">No conversations yet</p>
      </div>
    )
  }

  return (
    <div className="space-y-1">
      {chats.map((chat) => (
        <button
          key={chat.id}
          type="button"
          className={`w-full text-left p-3 rounded-md transition-colors ${
            selectedChatId === chat.id
              ? 'bg-gray-200 text-gray-900'
              : 'hover:bg-gray-100 text-gray-700'
          }`}
          onClick={() => onChatSelect(chat.id)}
        >
          <div className="truncate text-sm font-medium">
            {chat.topic}
          </div>
          <div className="text-xs text-gray-500 mt-1">
            {new Date(chat.created_at).toLocaleDateString()}
          </div>
        </button>
      ))}
    </div>
  )
}
