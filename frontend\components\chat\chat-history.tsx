'use client'

import { useState, useEffect } from 'react'
import { MessageSquare } from 'lucide-react'
import { chatAPI } from '@/lib/api'

interface Chat {
  id: string
  topic: string
  status: string
  created_at: string
}

interface ChatHistoryProps {
  selectedChatId: string | null
  onChatSelect: (chatId: string) => void
}

export function ChatHistory({ selectedChatId, onChatSelect }: ChatHistoryProps) {
  const [chats, setChats] = useState<Chat[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchChats = async () => {
      setLoading(true)
      try {
        const response = await chatAPI.getHistory()
        setChats(response.data)
      } catch (error) {
        console.error('Error fetching chat history:', error)
        // TODO: Handle error (e.g., show error message)
      } finally {
        setLoading(false)
      }
    }

    fetchChats()
  }, [])

  if (loading) {
    return (
      <div className="space-y-2">
        {[1, 2, 3].map((i) => (
          <div key={i} className="mx-2 h-10 bg-gray-800 animate-pulse rounded-lg" />
        ))}
      </div>
    )
  }

  if (chats.length === 0) {
    return (
      <div className="p-4 text-center">
        <MessageSquare className="h-6 w-6 text-gray-400 mx-auto mb-2" />
        <p className="text-sm text-gray-400">No conversations yet</p>
      </div>
    )
  }

  return (
    <div className="space-y-1">
      {chats.map((chat) => (
        <button
          key={chat.id}
          type="button"
          className={`w-full text-left px-3 py-2 mx-2 rounded-lg transition-colors hover:bg-gray-800 ${
            selectedChatId === chat.id ? 'bg-gray-800' : ''
          }`}
          onClick={() => onChatSelect(chat.id)}
        >
          <div className="truncate text-sm text-gray-200">
            {chat.topic}
          </div>
          <div className="text-xs text-gray-400 mt-1">
            {new Date(chat.created_at).toLocaleDateString()}
          </div>
        </button>
      ))}
    </div>
  )
}
