'use client'

import { useState, useEffect } from 'react'
import { MessageSquare } from 'lucide-react'
import { chatAPI } from '@/lib/api'

interface Chat {
  id: string
  topic: string
  status: string
  created_at: string
}

interface ChatHistoryProps {
  selectedChatId: string | null
  onChatSelect: (chatId: string) => void
}

export function ChatHistory({ selectedChatId, onChatSelect }: ChatHistoryProps) {
  const [chats, setChats] = useState<Chat[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchChats = async () => {
      setLoading(true)
      try {
        const response = await chatAPI.getHistory()
        setChats(response.data)
      } catch (error) {
        console.error('Error fetching chat history:', error)
        // TODO: Handle error (e.g., show error message)
      } finally {
        setLoading(false)
      }
    }

    fetchChats()
  }, [])

  if (loading) {
    return (
      <div className="space-y-3">
        {[1, 2, 3].map((i) => (
          <div key={i} className="h-14 bg-[#1a1a1a] animate-pulse rounded-xl border border-[#222222]" />
        ))}
      </div>
    )
  }

  if (chats.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-12 h-12 bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl flex items-center justify-center mx-auto mb-4 border border-gray-700">
          <MessageSquare className="h-6 w-6 text-gray-500" />
        </div>
        <p className="text-sm font-medium text-gray-400 mb-1">No conversations yet</p>
        <p className="text-xs text-gray-600">Start a new research session to begin</p>
      </div>
    )
  }

  return (
    <div className="space-y-2">
      {chats.map((chat) => (
        <button
          key={chat.id}
          type="button"
          className={`chat-item ${selectedChatId === chat.id ? 'active' : ''}`}
          onClick={() => onChatSelect(chat.id)}
        >
          {/* Subtle gradient overlay for selected state */}
          {selectedChatId === chat.id && (
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 pointer-events-none" />
          )}

          <div className="relative flex items-start gap-3">
            <div className={`w-2 h-2 rounded-full mt-2 transition-all duration-300 ${
              selectedChatId === chat.id
                ? 'bg-gradient-to-r from-blue-400 to-purple-400 shadow-lg shadow-blue-400/50'
                : 'bg-gray-600 group-hover:bg-gray-500'
            }`} />
            <div className="flex-1 min-w-0">
              <div className={`truncate text-sm font-medium transition-colors ${
                selectedChatId === chat.id ? 'text-white' : 'text-gray-300 group-hover:text-white'
              }`}>
                {chat.topic}
              </div>
              <div className="flex items-center gap-2 mt-1">
                <div className={`text-xs transition-colors ${
                  selectedChatId === chat.id ? 'text-blue-300' : 'text-gray-500 group-hover:text-gray-400'
                }`}>
                  {new Date(chat.created_at).toLocaleDateString()}
                </div>
                <div className={`px-2 py-0.5 rounded-full text-xs font-medium transition-all ${
                  chat.status === 'completed' ? 'bg-emerald-500/20 text-emerald-400 border border-emerald-500/30' :
                  chat.status === 'running' ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30' :
                  chat.status === 'failed' ? 'bg-red-500/20 text-red-400 border border-red-500/30' :
                  'bg-gray-500/20 text-gray-400 border border-gray-500/30'
                }`}>
                  {chat.status}
                </div>
              </div>
            </div>
          </div>
        </button>
      ))}
    </div>
  )
}
