'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/card'
import { Clock, MessageSquare } from 'lucide-react'
import { chatAPI } from '@/lib/api'

interface Chat {
  id: string
  topic: string
  status: string
  created_at: string
}

interface ChatHistoryProps {
  selectedChatId: string | null
  onChatSelect: (chatId: string) => void
}

export function ChatHistory({ selectedChatId, onChatSelect }: ChatHistoryProps) {
  const [chats, setChats] = useState<Chat[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchChats = async () => {
      setLoading(true)
      try {
        const response = await chatAPI.getHistory()
        setChats(response.data)
      } catch (error) {
        console.error('Error fetching chat history:', error)
        // TODO: Handle error (e.g., show error message)
      } finally {
        setLoading(false)
      }
    }

    fetchChats()
  }, [])

  if (loading) {
    return (
      <div className="p-4">
        <div className="space-y-2">
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-16 bg-muted animate-pulse rounded-lg" />
          ))}
        </div>
      </div>
    )
  }

  if (chats.length === 0) {
    return (
      <div className="p-4 text-center">
        <MessageSquare className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
        <p className="text-sm text-muted-foreground">No chat history yet</p>
        <p className="text-xs text-muted-foreground mt-1">
          Start a new research session to begin
        </p>
      </div>
    )
  }

  return (
    <div className="p-4 space-y-2">
      {chats.map((chat) => (
        <Card
          key={chat.id}
          className={`p-3 cursor-pointer transition-colors hover:bg-accent ${
            selectedChatId === chat.id ? 'bg-accent' : ''
          }`}
          onClick={() => onChatSelect(chat.id)}
        >
          <div className="space-y-1">
            <div className="font-medium text-sm truncate">{chat.topic}</div>
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Clock className="h-3 w-3" />
              <span>{new Date(chat.created_at).toLocaleDateString()}</span>
              <span className={`px-2 py-1 rounded-full text-xs ${
                chat.status === 'completed' ? 'bg-green-100 text-green-800' :
                chat.status === 'running' ? 'bg-blue-100 text-blue-800' :
                chat.status === 'failed' ? 'bg-red-100 text-red-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {chat.status}
              </span>
            </div>
          </div>
        </Card>
      ))}
    </div>
  )
}
