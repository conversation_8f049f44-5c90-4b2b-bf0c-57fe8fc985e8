'use client'

import { useState, useEffect } from 'react'
import { MessageSquare } from 'lucide-react'
import { chatAPI } from '@/lib/api'

interface Chat {
  id: string
  topic: string
  status: string
  created_at: string
}

interface ChatHistoryProps {
  selectedChatId: string | null
  onChatSelect: (chatId: string) => void
}

export function ChatHistory({ selectedChatId, onChatSelect }: ChatHistoryProps) {
  const [chats, setChats] = useState<Chat[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchChats = async () => {
      setLoading(true)
      try {
        const response = await chatAPI.getHistory()
        setChats(response.data)
      } catch (error) {
        console.error('Error fetching chat history:', error)
        // TODO: Handle error (e.g., show error message)
      } finally {
        setLoading(false)
      }
    }

    fetchChats()
  }, [])

  if (loading) {
    return (
      <div className="space-y-3">
        {[1, 2, 3].map((i) => (
          <div key={i} className="h-16 bg-gray-100 animate-pulse rounded-xl" />
        ))}
      </div>
    )
  }

  if (chats.length === 0) {
    return (
      <div className="text-center py-8">
        <MessageSquare className="h-8 w-8 text-gray-300 mx-auto mb-3" />
        <p className="text-sm text-gray-500 font-medium">No conversations yet</p>
        <p className="text-xs text-gray-400 mt-1">Start a new research session to begin</p>
      </div>
    )
  }

  return (
    <div className="space-y-2">
      {chats.map((chat) => (
        <button
          key={chat.id}
          type="button"
          className={`w-full text-left p-4 rounded-xl transition-all duration-200 group ${
            selectedChatId === chat.id
              ? 'bg-blue-50 border border-blue-200 shadow-sm'
              : 'hover:bg-gray-50 border border-transparent'
          }`}
          onClick={() => onChatSelect(chat.id)}
        >
          <div className="flex items-start gap-3">
            <div className={`w-2 h-2 rounded-full mt-2 ${
              selectedChatId === chat.id ? 'bg-blue-500' : 'bg-gray-300'
            }`} />
            <div className="flex-1 min-w-0">
              <div className={`truncate text-sm font-medium ${
                selectedChatId === chat.id ? 'text-blue-900' : 'text-gray-900'
              }`}>
                {chat.topic}
              </div>
              <div className="flex items-center gap-2 mt-1">
                <div className={`text-xs ${
                  selectedChatId === chat.id ? 'text-blue-600' : 'text-gray-500'
                }`}>
                  {new Date(chat.created_at).toLocaleDateString()}
                </div>
                <div className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                  chat.status === 'completed' ? 'bg-green-100 text-green-700' :
                  chat.status === 'running' ? 'bg-blue-100 text-blue-700' :
                  chat.status === 'failed' ? 'bg-red-100 text-red-700' :
                  'bg-gray-100 text-gray-600'
                }`}>
                  {chat.status}
                </div>
              </div>
            </div>
          </div>
        </button>
      ))}
    </div>
  )
}
