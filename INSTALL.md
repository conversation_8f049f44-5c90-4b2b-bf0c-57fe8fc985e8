# 🚀 Quick Installation Guide

Your Neon database and API keys are already configured! Follow these steps to get your modern CrewAI UI running.

## ✅ Pre-configured for You

- ✅ **Neon Database**: Connected to your database
- ✅ **API Keys**: Gemini and Serper keys added
- ✅ **Environment Files**: Backend and frontend configured

## 🏃‍♂️ Quick Start (5 minutes)

### Step 1: Install Backend Dependencies
```bash
cd backend
pip install -r requirements.txt
```

### Step 2: Install Frontend Dependencies
```bash
cd frontend
npm install
```

### Step 3: Setup Database & Admin User
```bash
# From the root directory
python quick-setup.py
```

### Step 4: Start the Application
```bash
# Option 1: Use startup scripts (recommended)
# Windows:
start.bat

# Mac/Linux:
chmod +x start.sh
./start.sh

# Option 2: Manual start (two terminals)
# Terminal 1 - Backend:
cd backend
uvicorn main:app --reload

# Terminal 2 - Frontend:
cd frontend
npm run dev
```

### Step 5: Access Your App
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## 🔐 Login Credentials

**Admin Account:**
- Email: `<EMAIL>`
- Password: `admin123`

⚠️ **Important**: Change the admin password after first login!

## 🎯 What You'll See

1. **Beautiful Login Page** - Modern gradient design
2. **Dashboard** - Three-panel layout with:
   - Left sidebar: Chat history (collapsible)
   - Center: Real-time chat interface
   - Right sidebar: File outputs (collapsible)
3. **Real-time Updates** - Watch your agents work live
4. **File Management** - View and download generated reports

## 🔧 Your Configuration

**Database**: 
```
**********************************************************************************************
```

**API Keys**:
- ✅ Gemini API Key: Configured
- ✅ Serper API Key: Configured
- ⚠️ OpenAI API Key: Add if needed

## 🎨 Features Ready to Use

### For Admins
- Create and manage users
- Monitor system activity
- Access all chat histories

### For Users
- Start new research sessions
- Real-time progress tracking
- File download and preview
- Chat history management

## 🐛 Troubleshooting

### Database Issues
```bash
# Test connection
python quick-setup.py
```

### Port Conflicts
- Backend runs on port 8000
- Frontend runs on port 3000
- Make sure these ports are available

### Missing Dependencies
```bash
# Backend
cd backend
pip install -r requirements.txt

# Frontend
cd frontend
npm install
```

## 🎉 Next Steps

1. **Login** with admin credentials
2. **Create users** for your team
3. **Start researching** with your AI agents
4. **Explore features** like real-time updates and file management

## 📞 Need Help?

- Check the console logs for errors
- Visit http://localhost:8000/docs for API documentation
- Ensure all dependencies are installed
- Verify your internet connection for database access

---

**Your modern CrewAI interface is ready! 🤖✨**
