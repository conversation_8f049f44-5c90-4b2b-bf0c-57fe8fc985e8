from fastapi import WebSocket
from typing import Dict, List
import json
from datetime import datetime

class WebSocketManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
    
    async def connect(self, websocket: WebSocket, user_id: str):
        """Connect a new WebSocket client"""
        await websocket.accept()
        self.active_connections[user_id] = websocket
        print(f"User {user_id} connected via WebSocket")
    
    def disconnect(self, user_id: str):
        """Disconnect a WebSocket client"""
        if user_id in self.active_connections:
            del self.active_connections[user_id]
            print(f"User {user_id} disconnected from WebSocket")
    
    async def send_personal_message(self, message: dict, user_id: str):
        """Send a message to a specific user"""
        if user_id in self.active_connections:
            try:
                await self.active_connections[user_id].send_text(json.dumps(message))
            except Exception as e:
                print(f"Error sending message to user {user_id}: {e}")
                # Remove disconnected client
                self.disconnect(user_id)
    
    async def broadcast(self, message: dict):
        """Broadcast a message to all connected clients"""
        disconnected_users = []
        for user_id, connection in self.active_connections.items():
            try:
                await connection.send_text(json.dumps(message))
            except Exception as e:
                print(f"Error broadcasting to user {user_id}: {e}")
                disconnected_users.append(user_id)
        
        # Clean up disconnected clients
        for user_id in disconnected_users:
            self.disconnect(user_id)
    
    async def send_agent_update(self, user_id: str, chat_id: str, agent_name: str, message: str, message_type: str = "info", metadata: dict = None):
        """Send an agent update to a specific user"""
        update = {
            "type": "agent_update",
            "chat_id": chat_id,
            "agent_name": agent_name,
            "message": message,
            "message_type": message_type,
            "metadata": metadata or {},
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.send_personal_message(update, user_id)
    
    async def send_progress_update(self, user_id: str, chat_id: str, progress: int, status: str, current_agent: str = None):
        """Send a progress update to a specific user"""
        update = {
            "type": "progress_update",
            "chat_id": chat_id,
            "progress": progress,
            "status": status,
            "current_agent": current_agent,
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.send_personal_message(update, user_id)
    
    async def send_file_update(self, user_id: str, chat_id: str, file_info: dict):
        """Send a file update to a specific user"""
        update = {
            "type": "file_update",
            "chat_id": chat_id,
            "file_info": file_info,
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.send_personal_message(update, user_id)
    
    async def send_completion_update(self, user_id: str, chat_id: str, success: bool, final_output: str = None, error: str = None):
        """Send a completion update to a specific user"""
        update = {
            "type": "completion_update",
            "chat_id": chat_id,
            "success": success,
            "final_output": final_output,
            "error": error,
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.send_personal_message(update, user_id)
