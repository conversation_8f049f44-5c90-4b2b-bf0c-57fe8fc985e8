import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: string | Date) {
  const d = new Date(date)
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

export function formatFileSize(bytes: number) {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export function getFileIcon(fileType: string) {
  switch (fileType?.toLowerCase()) {
    case 'md':
    case 'markdown':
      return '📝'
    case 'json':
      return '📋'
    case 'txt':
      return '📄'
    case 'html':
      return '🌐'
    case 'pdf':
      return '📕'
    default:
      return '📄'
  }
}

export function truncateText(text: string, maxLength: number) {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

export function getAgentColor(agentName: string) {
  const colors = {
    'information_retrieval_specialist': 'bg-blue-100 text-blue-800 border-blue-200',
    'research_analyst': 'bg-green-100 text-green-800 border-green-200',
    'research_synthesizer': 'bg-purple-100 text-purple-800 border-purple-200',
    'system': 'bg-gray-100 text-gray-800 border-gray-200',
  }
  
  return colors[agentName as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200'
}

export function getStatusColor(status: string) {
  const colors = {
    'pending': 'bg-yellow-100 text-yellow-800 border-yellow-200',
    'running': 'bg-blue-100 text-blue-800 border-blue-200',
    'completed': 'bg-green-100 text-green-800 border-green-200',
    'failed': 'bg-red-100 text-red-800 border-red-200',
  }
  
  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200'
}

export function formatAgentName(agentName: string) {
  return agentName
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}
