{"name": "gemini-crewai-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "14.0.3", "react": "^18", "react-dom": "^18", "typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "tailwindcss": "^3.3.0", "autoprefixer": "^10.0.1", "postcss": "^8", "@tailwindcss/typography": "^0.5.10", "lucide-react": "^0.294.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-button": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "axios": "^1.6.2", "zustand": "^4.4.7", "react-syntax-highlighter": "^15.5.0", "@types/react-syntax-highlighter": "^15.5.11"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.0.3"}}