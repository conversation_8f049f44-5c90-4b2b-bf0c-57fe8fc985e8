import { _class_apply_descriptor_get } from "./_class_apply_descriptor_get.js";
import { _class_check_private_static_access } from "./_class_check_private_static_access.js";
import { _class_check_private_static_field_descriptor } from "./_class_check_private_static_field_descriptor.js";

export function _class_static_private_field_spec_get(receiver, classConstructor, descriptor) {
    _class_check_private_static_access(receiver, classConstructor);
    _class_check_private_static_field_descriptor(descriptor, "get");

    return _class_apply_descriptor_get(receiver, descriptor);
}
export { _class_static_private_field_spec_get as _ };
