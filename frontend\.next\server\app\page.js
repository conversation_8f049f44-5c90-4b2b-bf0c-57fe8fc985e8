/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp%5Cpage.tsx&server=true!":
/*!**************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp%5Cpage.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDd3l6ZXMlNUNEZXNrdG9wJTVDR29vZ2xlJTVDZ29vZ2xlX2NyZXdhaSU1Q2Zyb250ZW5kJTVDYXBwJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ2VtaW5pLWNyZXdhaS1mcm9udGVuZC8/ZmVlNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHd5emVzXFxcXERlc2t0b3BcXFxcR29vZ2xlXFxcXGdvb2dsZV9jcmV3YWlcXFxcZnJvbnRlbmRcXFxcYXBwXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp%5Cproviders.tsx&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp%5Cglobals.css&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp%5Cproviders.tsx&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp%5Cglobals.css&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers.tsx */ \"(ssr)/./app/providers.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDd3l6ZXMlNUNEZXNrdG9wJTVDR29vZ2xlJTVDZ29vZ2xlX2NyZXdhaSU1Q2Zyb250ZW5kJTVDYXBwJTVDcHJvdmlkZXJzLnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q3d5emVzJTVDRGVza3RvcCU1Q0dvb2dsZSU1Q2dvb2dsZV9jcmV3YWklNUNmcm9udGVuZCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q3d5emVzJTVDRGVza3RvcCU1Q0dvb2dsZSU1Q2dvb2dsZV9jcmV3YWklNUNmcm9udGVuZCU1Q2FwcCU1Q2dsb2JhbHMuY3NzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2dlbWluaS1jcmV3YWktZnJvbnRlbmQvPzQ2OWIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFx3eXplc1xcXFxEZXNrdG9wXFxcXEdvb2dsZVxcXFxnb29nbGVfY3Jld2FpXFxcXGZyb250ZW5kXFxcXGFwcFxcXFxwcm92aWRlcnMudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp%5Cproviders.tsx&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp%5Cglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_stores_auth_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/stores/auth-store */ \"(ssr)/./lib/stores/auth-store.ts\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction HomePage() {\n    const { isAuthenticated, isLoading } = (0,_lib_stores_auth_store__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading) {\n            if (isAuthenticated) {\n                router.push(\"/dashboard\");\n            } else {\n                router.push(\"/login\");\n            }\n        }\n    }, [\n        isAuthenticated,\n        isLoading,\n        router\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-12 w-12 text-blue-600 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-2\",\n                    children: \"Gemini CrewAI\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center gap-2 text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-4 w-4 animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Loading...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\page.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\page.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stores/auth-store */ \"(ssr)/./lib/stores/auth-store.ts\");\n/* harmony import */ var _lib_stores_websocket_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/stores/websocket-store */ \"(ssr)/./lib/stores/websocket-store.ts\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\nfunction Providers({ children }) {\n    const { initializeAuth } = (0,_lib_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    const { connect, disconnect } = (0,_lib_stores_websocket_store__WEBPACK_IMPORTED_MODULE_3__.useWebSocketStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize authentication on app start\n        initializeAuth();\n        // Cleanup on unmount\n        return ()=>{\n            disconnect();\n        };\n    }, [\n        initializeAuth,\n        disconnect\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvcHJvdmlkZXJzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUVpQztBQUNxQjtBQUNVO0FBRXpELFNBQVNHLFVBQVUsRUFBRUMsUUFBUSxFQUFpQztJQUNuRSxNQUFNLEVBQUVDLGNBQWMsRUFBRSxHQUFHSixvRUFBWUE7SUFDdkMsTUFBTSxFQUFFSyxPQUFPLEVBQUVDLFVBQVUsRUFBRSxHQUFHTCw4RUFBaUJBO0lBRWpERixnREFBU0EsQ0FBQztRQUNSLHlDQUF5QztRQUN6Q0s7UUFFQSxxQkFBcUI7UUFDckIsT0FBTztZQUNMRTtRQUNGO0lBQ0YsR0FBRztRQUFDRjtRQUFnQkU7S0FBVztJQUUvQixxQkFBTztrQkFBR0g7O0FBQ1oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9nZW1pbmktY3Jld2FpLWZyb250ZW5kLy4vYXBwL3Byb3ZpZGVycy50c3g/Y2U0NiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VBdXRoU3RvcmUgfSBmcm9tICdAL2xpYi9zdG9yZXMvYXV0aC1zdG9yZSdcbmltcG9ydCB7IHVzZVdlYlNvY2tldFN0b3JlIH0gZnJvbSAnQC9saWIvc3RvcmVzL3dlYnNvY2tldC1zdG9yZSdcblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XG4gIGNvbnN0IHsgaW5pdGlhbGl6ZUF1dGggfSA9IHVzZUF1dGhTdG9yZSgpXG4gIGNvbnN0IHsgY29ubmVjdCwgZGlzY29ubmVjdCB9ID0gdXNlV2ViU29ja2V0U3RvcmUoKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gSW5pdGlhbGl6ZSBhdXRoZW50aWNhdGlvbiBvbiBhcHAgc3RhcnRcbiAgICBpbml0aWFsaXplQXV0aCgpXG5cbiAgICAvLyBDbGVhbnVwIG9uIHVubW91bnRcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgZGlzY29ubmVjdCgpXG4gICAgfVxuICB9LCBbaW5pdGlhbGl6ZUF1dGgsIGRpc2Nvbm5lY3RdKVxuXG4gIHJldHVybiA8PntjaGlsZHJlbn08Lz5cbn1cbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VBdXRoU3RvcmUiLCJ1c2VXZWJTb2NrZXRTdG9yZSIsIlByb3ZpZGVycyIsImNoaWxkcmVuIiwiaW5pdGlhbGl6ZUF1dGgiLCJjb25uZWN0IiwiZGlzY29ubmVjdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminAPI: () => (/* binding */ adminAPI),\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   authAPI: () => (/* binding */ authAPI),\n/* harmony export */   chatAPI: () => (/* binding */ chatAPI),\n/* harmony export */   crewAPI: () => (/* binding */ crewAPI),\n/* harmony export */   filesAPI: () => (/* binding */ filesAPI)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: `${API_BASE_URL}/api`,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Request interceptor to add auth token\napi.interceptors.request.use((config)=>{\n    // Token will be set by auth store\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor to handle errors\napi.interceptors.response.use((response)=>{\n    return response;\n}, (error)=>{\n    if (error.response?.status === 401) {\n        // Token expired or invalid\n        // The auth store will handle logout\n        window.location.href = \"/login\";\n    }\n    return Promise.reject(error);\n});\n// API functions\nconst authAPI = {\n    login: (email, password)=>api.post(\"/auth/login\", {\n            email,\n            password\n        }),\n    getMe: ()=>api.get(\"/auth/me\"),\n    verifyToken: ()=>api.post(\"/auth/verify-token\")\n};\nconst chatAPI = {\n    getHistory: (limit = 50, offset = 0)=>api.get(`/chat/history?limit=${limit}&offset=${offset}`),\n    getChat: (chatId)=>api.get(`/chat/${chatId}`),\n    deleteChat: (chatId)=>api.delete(`/chat/${chatId}`),\n    getMessages: (chatId)=>api.get(`/chat/${chatId}/messages`)\n};\nconst crewAPI = {\n    execute: (topic)=>api.post(\"/crew/execute\", {\n            topic\n        })\n};\nconst filesAPI = {\n    getChatFiles: (chatId)=>api.get(`/files/chat/${chatId}`),\n    getFile: (fileId)=>api.get(`/files/${fileId}`),\n    downloadFile: (fileId)=>api.get(`/files/${fileId}/download`),\n    deleteFile: (fileId)=>api.delete(`/files/${fileId}`)\n};\nconst adminAPI = {\n    getUsers: (limit = 100, offset = 0)=>api.get(`/admin/users?limit=${limit}&offset=${offset}`),\n    createUser: (userData)=>api.post(\"/admin/users\", userData),\n    updateUser: (userId, userData)=>api.put(`/admin/users/${userId}`, userData),\n    deleteUser: (userId)=>api.delete(`/admin/users/${userId}`)\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./lib/stores/auth-store.ts":
/*!**********************************!*\
  !*** ./lib/stores/auth-store.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)((set, get)=>({\n        user: null,\n        token: null,\n        isLoading: false,\n        isAuthenticated: false,\n        login: async (email, password)=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.api.post(\"/auth/login\", {\n                    email,\n                    password\n                });\n                const { access_token, user } = response.data;\n                set({\n                    token: access_token,\n                    user,\n                    isAuthenticated: true,\n                    isLoading: false\n                });\n                // Set token for future requests\n                _lib_api__WEBPACK_IMPORTED_MODULE_0__.api.defaults.headers.common[\"Authorization\"] = `Bearer ${access_token}`;\n                // Store in localStorage\n                localStorage.setItem(\"auth-token\", access_token);\n                localStorage.setItem(\"auth-user\", JSON.stringify(user));\n            } catch (error) {\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        logout: ()=>{\n            set({\n                user: null,\n                token: null,\n                isAuthenticated: false\n            });\n            // Remove token from API headers\n            delete _lib_api__WEBPACK_IMPORTED_MODULE_0__.api.defaults.headers.common[\"Authorization\"];\n            // Clear localStorage\n            localStorage.removeItem(\"auth-token\");\n            localStorage.removeItem(\"auth-user\");\n        },\n        initializeAuth: async ()=>{\n            try {\n                const token = localStorage.getItem(\"auth-token\");\n                const userStr = localStorage.getItem(\"auth-user\");\n                if (token && userStr) {\n                    const user = JSON.parse(userStr);\n                    // Set token for API requests\n                    _lib_api__WEBPACK_IMPORTED_MODULE_0__.api.defaults.headers.common[\"Authorization\"] = `Bearer ${token}`;\n                    // Verify token is still valid\n                    const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.api.get(\"/auth/me\");\n                    set({\n                        token,\n                        user: response.data,\n                        isAuthenticated: true\n                    });\n                }\n            } catch (error) {\n                // Token is invalid, logout\n                get().logout();\n            }\n        },\n        setLoading: (loading)=>{\n            set({\n                isLoading: loading\n            });\n        }\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/stores/auth-store.ts\n");

/***/ }),

/***/ "(ssr)/./lib/stores/websocket-store.ts":
/*!***************************************!*\
  !*** ./lib/stores/websocket-store.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWebSocketStore: () => (/* binding */ useWebSocketStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n\nconst useWebSocketStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        socket: null,\n        isConnected: false,\n        messages: [],\n        currentProgress: 0,\n        currentStatus: \"idle\",\n        currentAgent: null,\n        connect: (userId)=>{\n            const wsUrl = \"ws://localhost:8000\" || 0;\n            const socket = new WebSocket(`${wsUrl}/ws/${userId}`);\n            socket.onopen = ()=>{\n                console.log(\"WebSocket connected\");\n                set({\n                    isConnected: true\n                });\n                // Send ping to keep connection alive\n                const pingInterval = setInterval(()=>{\n                    if (socket.readyState === WebSocket.OPEN) {\n                        socket.send(JSON.stringify({\n                            type: \"ping\"\n                        }));\n                    } else {\n                        clearInterval(pingInterval);\n                    }\n                }, 30000) // Ping every 30 seconds\n                ;\n            };\n            socket.onmessage = (event)=>{\n                try {\n                    const message = JSON.parse(event.data);\n                    // Handle different message types\n                    switch(message.type){\n                        case \"agent_update\":\n                            set((state)=>({\n                                    messages: [\n                                        ...state.messages,\n                                        message\n                                    ]\n                                }));\n                            break;\n                        case \"progress_update\":\n                            set({\n                                currentProgress: message.progress || 0,\n                                currentStatus: message.status || \"idle\",\n                                currentAgent: message.current_agent || null\n                            });\n                            break;\n                        case \"file_update\":\n                            set((state)=>({\n                                    messages: [\n                                        ...state.messages,\n                                        message\n                                    ]\n                                }));\n                            break;\n                        case \"completion_update\":\n                            set({\n                                currentProgress: message.success ? 100 : 0,\n                                currentStatus: message.success ? \"completed\" : \"failed\",\n                                currentAgent: null\n                            });\n                            set((state)=>({\n                                    messages: [\n                                        ...state.messages,\n                                        message\n                                    ]\n                                }));\n                            break;\n                        case \"pong\":\n                            break;\n                        default:\n                            console.log(\"Unknown message type:\", message.type);\n                    }\n                } catch (error) {\n                    console.error(\"Error parsing WebSocket message:\", error);\n                }\n            };\n            socket.onclose = ()=>{\n                console.log(\"WebSocket disconnected\");\n                set({\n                    isConnected: false,\n                    socket: null\n                });\n            };\n            socket.onerror = (error)=>{\n                console.error(\"WebSocket error:\", error);\n                set({\n                    isConnected: false\n                });\n            };\n            set({\n                socket\n            });\n        },\n        disconnect: ()=>{\n            const { socket } = get();\n            if (socket) {\n                socket.close();\n                set({\n                    socket: null,\n                    isConnected: false\n                });\n            }\n        },\n        sendMessage: (message)=>{\n            const { socket, isConnected } = get();\n            if (socket && isConnected) {\n                socket.send(JSON.stringify(message));\n            }\n        },\n        addMessage: (message)=>{\n            set((state)=>({\n                    messages: [\n                        ...state.messages,\n                        message\n                    ]\n                }));\n        },\n        clearMessages: ()=>{\n            set({\n                messages: [],\n                currentProgress: 0,\n                currentStatus: \"idle\",\n                currentAgent: null\n            });\n        }\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/stores/websocket-store.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"c69f6ae379bc\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9nZW1pbmktY3Jld2FpLWZyb250ZW5kLy4vYXBwL2dsb2JhbHMuY3NzPzNmODgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJjNjlmNmFlMzc5YmNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./app/providers.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"Gemini CrewAI - Intelligent Agent Platform\",\n    description: \"Modern UI for CrewAI Agent System - Research and Analysis Platform\",\n    keywords: [\n        \"AI\",\n        \"Agents\",\n        \"Research\",\n        \"Analysis\",\n        \"CrewAI\"\n    ],\n    authors: [\n        {\n            name: \"Gemini CrewAI Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-background\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Google\\\\google_crewai\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Google\google_crewai\frontend\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Google\google_crewai\frontend\app\providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Google\google_crewai\frontend\app\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/axios","vendor-chunks/mime-db","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/lucide-react","vendor-chunks/asynckit","vendor-chunks/use-sync-external-store","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/zustand","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/es-define-property","vendor-chunks/gopd","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwyzes%5CDesktop%5CGoogle%5Cgoogle_crewai%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();