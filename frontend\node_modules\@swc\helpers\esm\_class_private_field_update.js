import { _class_apply_descriptor_update } from "./_class_apply_descriptor_update.js";
import { _class_extract_field_descriptor } from "./_class_extract_field_descriptor.js";

export function _class_private_field_update(receiver, privateMap) {
    var descriptor = _class_extract_field_descriptor(receiver, privateMap, "update");
    return _class_apply_descriptor_update(receiver, descriptor);
}
export { _class_private_field_update as _ };
