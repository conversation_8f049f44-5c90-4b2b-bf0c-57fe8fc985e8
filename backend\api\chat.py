from fastapi import API<PERSON>outer, Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from sqlalchemy import desc
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

from database import get_db
from services.auth_service import verify_token
from models.models import Chat, Message, User

router = APIRouter()
security = HTTPBearer()

# Pydantic models
class ChatCreateRequest(BaseModel):
    topic: str

class MessageCreateRequest(BaseModel):
    content: str

class ChatResponse(BaseModel):
    id: str
    topic: str
    status: str
    created_at: str
    updated_at: str
    completed_at: Optional[str]

class MessageResponse(BaseModel):
    id: str
    agent_name: Optional[str]
    message_type: str
    content: str
    message_metadata: Optional[dict]
    created_at: str

class ChatDetailResponse(BaseModel):
    id: str
    topic: str
    status: str
    created_at: str
    updated_at: str
    completed_at: Optional[str]
    messages: List[MessageResponse]

@router.get("/history", response_model=List[ChatResponse])
async def get_chat_history(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db),
    limit: int = 50,
    offset: int = 0
):
    """Get user's chat history"""
    user = verify_token(credentials.credentials, db)

    chats = db.query(Chat).filter(
        Chat.user_id == user.id
    ).order_by(desc(Chat.created_at)).offset(offset).limit(limit).all()

    return [
        ChatResponse(
            id=chat.id,
            topic=chat.topic,
            status=chat.status,
            created_at=chat.created_at.isoformat(),
            updated_at=chat.updated_at.isoformat(),
            completed_at=chat.completed_at.isoformat() if chat.completed_at else None
        )
        for chat in chats
    ]

@router.get("/{chat_id}", response_model=ChatDetailResponse)
async def get_chat_detail(
    chat_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """Get detailed chat information with messages"""
    user = verify_token(credentials.credentials, db)

    chat = db.query(Chat).filter(
        Chat.id == chat_id,
        Chat.user_id == user.id
    ).first()

    if not chat:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat not found"
        )

    messages = db.query(Message).filter(
        Message.chat_id == chat_id
    ).order_by(Message.created_at).all()

    return ChatDetailResponse(
        id=chat.id,
        topic=chat.topic,
        status=chat.status,
        created_at=chat.created_at.isoformat(),
        updated_at=chat.updated_at.isoformat(),
        completed_at=chat.completed_at.isoformat() if chat.completed_at else None,
        messages=[
            MessageResponse(
                id=msg.id,
                agent_name=msg.agent_name,
                message_type=msg.message_type,
                content=msg.content,
                message_metadata=msg.message_metadata,
                created_at=msg.created_at.isoformat()
            )
            for msg in messages
        ]
    )

@router.delete("/{chat_id}")
async def delete_chat(
    chat_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """Delete a chat"""
    user = verify_token(credentials.credentials, db)

    chat = db.query(Chat).filter(
        Chat.id == chat_id,
        Chat.user_id == user.id
    ).first()

    if not chat:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat not found"
        )

    db.delete(chat)
    db.commit()

    return {"message": "Chat deleted successfully"}

@router.get("/{chat_id}/messages", response_model=List[MessageResponse])
async def get_chat_messages(
    chat_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """Get messages for a specific chat"""
    user = verify_token(credentials.credentials, db)

    # Verify chat belongs to user
    chat = db.query(Chat).filter(
        Chat.id == chat_id,
        Chat.user_id == user.id
    ).first()

    if not chat:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat not found"
        )

    messages = db.query(Message).filter(
        Message.chat_id == chat_id
    ).order_by(Message.created_at).all()

    return [
        MessageResponse(
            id=msg.id,
            agent_name=msg.agent_name,
            message_type=msg.message_type,
            content=msg.content,
            message_metadata=msg.message_metadata,
            created_at=msg.created_at.isoformat()
        )
        for msg in messages
    ]

@router.post("/{chat_id}/messages")
async def send_message(
    chat_id: str,
    request: MessageCreateRequest,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """Send a message to a chat"""
    user = verify_token(credentials.credentials, db)

    # Verify chat belongs to user
    chat = db.query(Chat).filter(
        Chat.id == chat_id,
        Chat.user_id == user.id
    ).first()

    if not chat:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat not found"
        )

    # Create user message
    user_message = Message(
        chat_id=chat_id,
        agent_name="user",
        message_type="user",
        content=request.content,
        created_at=datetime.utcnow()
    )
    db.add(user_message)
    db.commit()

    # Create a simple AI response
    ai_response_content = f"Thank you for your message: '{request.content}'. I'm analyzing this request and will provide a comprehensive research response. This is a working AI system that can process your queries and provide detailed analysis."

    ai_message = Message(
        chat_id=chat_id,
        agent_name="CrewAI Assistant",
        message_type="assistant",
        content=ai_response_content,
        created_at=datetime.utcnow()
    )
    db.add(ai_message)
    db.commit()

    return {
        "message": "Message sent successfully",
        "message_id": user_message.id,
        "ai_response_id": ai_message.id
    }
