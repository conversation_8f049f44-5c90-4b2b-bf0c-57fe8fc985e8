from crewai import Agent, Crew, Process, Task
from crewai.project import Crew<PERSON><PERSON>, agent, crew, task

# For now, let's create agents without external tools to avoid validation issues
# We can add tools back once we resolve the compatibility issues
def get_search_tools():
    """Get properly configured search tools"""
    # Return empty list for now to avoid validation errors
    # TODO: Fix tool compatibility issues
    return []
# If you want to run a snippet of code before or after the crew starts,
# you can use the @before_kickoff and @after_kickoff decorators
# https://docs.crewai.com/concepts/crews#example-crew-class-with-decorators

@CrewBase
class Gemini():
    """Gemini crew"""

    # Learn more about YAML configuration files here:
    # Agents: https://docs.crewai.com/concepts/agents#yaml-configuration-recommended
    # Tasks: https://docs.crewai.com/concepts/tasks#yaml-configuration-recommended
    agents_config = 'config/agents.yaml'
    tasks_config = 'config/tasks.yaml'

    # If you would like to add tools to your agents, you can learn more about it here:
    # https://docs.crewai.com/concepts/agents#agent-tools
    @agent
    def information_retrieval_specialist(self) -> Agent:
        return Agent(
            config=self.agents_config['information_retrieval_specialist'],
            tools=get_search_tools(),
            verbose=True,
            allow_delegation=False
        )

    @agent
    def research_analyst(self) -> Agent:
        return Agent(
            config=self.agents_config['research_analyst'],
            verbose=True,
            allow_delegation=False,
            tools=get_search_tools()
        )

    @agent
    def research_synthesizer(self) -> Agent:
        return Agent(
            config=self.agents_config['research_synthesizer'],
            verbose=True,
            allow_delegation=False
        )

    # To learn more about structured task outputs,
    # task dependencies, and task callbacks, check out the documentation:
    # https://docs.crewai.com/concepts/tasks#overview-of-a-task
    @task
    def information_retrieval_task(self) -> Task:
        return Task(
            config=self.tasks_config['information_retrieval_task'],
        )

    @task
    def research_analyst_task(self) -> Task:
        return Task(
            config=self.tasks_config['research_analyst_task'],
            #output_file='report.md'
        )

    @task
    def research_synthesizer_task(self) -> Task:
        return Task(
            config=self.tasks_config['research_synthesizer_task'],
            output_file='knowledge/report.md'
        )

    @crew
    def crew(self) -> Crew:
        """Creates the Gemini crew"""
        # To learn how to add knowledge sources to your crew, check out the documentation:
        # https://docs.crewai.com/concepts/knowledge#what-is-knowledge

        return Crew(
            agents=self.agents, # Automatically created by the @agent decorator
            tasks=self.tasks, # Automatically created by the @task decorator
            process=Process.sequential,
            verbose=True,
            memory=False,  # Disable memory to avoid ChromaDB issues
            # process=Process.hierarchical, # In case you wanna use that instead https://docs.crewai.com/how-to/Hierarchical/
        )
