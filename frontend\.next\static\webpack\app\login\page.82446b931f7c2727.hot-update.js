"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminAPI: function() { return /* binding */ adminAPI; },\n/* harmony export */   api: function() { return /* binding */ api; },\n/* harmony export */   authAPI: function() { return /* binding */ authAPI; },\n/* harmony export */   chatAPI: function() { return /* binding */ chatAPI; },\n/* harmony export */   crewAPI: function() { return /* binding */ crewAPI; },\n/* harmony export */   filesAPI: function() { return /* binding */ filesAPI; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: \"\".concat(API_BASE_URL, \"/api\"),\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Request interceptor to add auth token\napi.interceptors.request.use((config)=>{\n    // Get token from localStorage\n    const token = localStorage.getItem(\"token\");\n    if (token) {\n        config.headers.Authorization = \"Bearer \".concat(token);\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor to handle errors\napi.interceptors.response.use((response)=>{\n    return response;\n}, (error)=>{\n    var _error_response;\n    if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n        // Token expired or invalid\n        // The auth store will handle logout\n        window.location.href = \"/login\";\n    }\n    return Promise.reject(error);\n});\n// API functions\nconst authAPI = {\n    login: (email, password)=>api.post(\"/auth/login\", {\n            email,\n            password\n        }),\n    getMe: ()=>api.get(\"/auth/me\"),\n    verifyToken: ()=>api.post(\"/auth/verify-token\")\n};\nconst chatAPI = {\n    getHistory: function() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 50, offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        return api.get(\"/chat/history?limit=\".concat(limit, \"&offset=\").concat(offset));\n    },\n    getChat: (chatId)=>api.get(\"/chat/\".concat(chatId)),\n    deleteChat: (chatId)=>api.delete(\"/chat/\".concat(chatId)),\n    clearAllChats: ()=>api.delete(\"/chat/all\"),\n    getMessages: (chatId)=>api.get(\"/chat/\".concat(chatId, \"/messages\")),\n    sendMessage: (chatId, content)=>api.post(\"/chat/\".concat(chatId, \"/messages\"), {\n            content\n        })\n};\nconst crewAPI = {\n    execute: (topic)=>api.post(\"/crew/execute\", {\n            topic\n        })\n};\nconst filesAPI = {\n    getChatFiles: (chatId)=>api.get(\"/files/chat/\".concat(chatId)),\n    getFile: (fileId)=>api.get(\"/files/\".concat(fileId)),\n    downloadFile: (fileId)=>api.get(\"/files/\".concat(fileId, \"/download\")),\n    deleteFile: (fileId)=>api.delete(\"/files/\".concat(fileId))\n};\nconst adminAPI = {\n    getUsers: function() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 100, offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        return api.get(\"/admin/users?limit=\".concat(limit, \"&offset=\").concat(offset));\n    },\n    createUser: (userData)=>api.post(\"/admin/users\", userData),\n    updateUser: (userId, userData)=>api.put(\"/admin/users/\".concat(userId), userData),\n    deleteUser: (userId)=>api.delete(\"/admin/users/\".concat(userId))\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ })

});