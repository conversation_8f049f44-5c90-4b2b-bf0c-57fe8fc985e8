'use client'

import { useEffect, useState } from 'react'
import { useAuthStore } from '@/lib/stores/auth-store'
import { useWebSocketStore } from '@/lib/stores/websocket-store'
import { useRouter } from 'next/navigation'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { ChatInterface } from '@/components/chat/chat-interface'
import { ChatHistory } from '@/components/chat/chat-history'
import { FileViewer } from '@/components/files/file-viewer'
import { NewChatDialog } from '@/components/chat/new-chat-dialog'
import { Button } from '@/components/ui/button'
import { Plus, Menu, X, FileText, Bot, Search } from 'lucide-react'
import { cn } from '@/lib/utils'

export default function DashboardPage() {
  const { user, isAuthenticated } = useAuthStore()
  const { connect } = useWebSocketStore()
  const router = useRouter()

  const [selectedChatId, setSelectedChatId] = useState<string | null>(null)
  const [isHistoryOpen, setIsHistoryOpen] = useState(true)
  const [isFilesOpen, setIsFilesOpen] = useState(false)
  const [isNewChatOpen, setIsNewChatOpen] = useState(false)

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login')
      return
    }

    // Connect to WebSocket
    if (user?.id) {
      connect(user.id)
    }
  }, [isAuthenticated, user, router, connect])

  const handleNewChat = () => {
    setIsNewChatOpen(true)
  }

  const handleChatCreated = (chatId: string) => {
    setSelectedChatId(chatId)
    setIsNewChatOpen(false)
  }

  const handleChatSelect = (chatId: string) => {
    setSelectedChatId(chatId)
  }

  if (!isAuthenticated || !user) {
    return null
  }

  return (
    <DashboardLayout>
      <div className="h-screen bg-gray-50 flex">
        {/* Left Sidebar - Modern Design */}
        <div className="w-80 bg-white border-r border-gray-200 flex flex-col shadow-sm">
          {/* Header */}
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <Bot className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="font-bold text-xl text-gray-900">CrewAI</h1>
                <p className="text-sm text-gray-500">Research Platform</p>
              </div>
            </div>

            <button
              type="button"
              onClick={handleNewChat}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl py-3 px-4 font-semibold text-sm hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
            >
              <Plus className="h-4 w-4" />
              New Research
            </button>
          </div>

          {/* Search */}
          <div className="p-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search conversations..."
                className="w-full bg-gray-50 border-0 rounded-xl pl-10 pr-4 py-3 text-sm text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white transition-all"
              />
            </div>
          </div>

          {/* Chat History */}
          <div className="flex-1 overflow-y-auto px-4">
            <ChatHistory
              selectedChatId={selectedChatId}
              onChatSelect={handleChatSelect}
            />
          </div>

          {/* Bottom Section */}
          <div className="p-4 border-t border-gray-100">
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
              <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
              <div className="flex-1">
                <div className="text-sm font-medium text-gray-900">Online</div>
                <div className="text-xs text-gray-500">All systems operational</div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col">
          {/* Chat Interface */}
          <div className="flex-1 overflow-hidden">
            <ChatInterface
              chatId={selectedChatId}
              onNewChat={handleNewChat}
            />
          </div>
        </div>

        {/* Right Sidebar - Collapsible */}
        {isFilesOpen && (
          <div className="w-80 bg-white border-l border-gray-200 flex flex-col shadow-sm">
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-center justify-between mb-4">
                <h2 className="font-bold text-lg text-gray-900">Research Files</h2>
                <button
                  type="button"
                  title="Close files panel"
                  onClick={() => setIsFilesOpen(false)}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <X className="h-4 w-4 text-gray-500" />
                </button>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <button type="button" className="p-3 bg-blue-50 hover:bg-blue-100 rounded-xl border border-blue-200 transition-colors text-center group">
                  <FileText className="h-5 w-5 mx-auto mb-2 text-blue-600 group-hover:text-blue-700" />
                  <span className="text-xs font-medium text-blue-700">Documents</span>
                </button>
                <button type="button" className="p-3 bg-purple-50 hover:bg-purple-100 rounded-xl border border-purple-200 transition-colors text-center group">
                  <Bot className="h-5 w-5 mx-auto mb-2 text-purple-600 group-hover:text-purple-700" />
                  <span className="text-xs font-medium text-purple-700">Agents</span>
                </button>
              </div>
            </div>

            <div className="flex-1 overflow-y-auto">
              <FileViewer chatId={selectedChatId} />
            </div>
          </div>
        )}

        {/* Floating Files Toggle Button */}
        {!isFilesOpen && (
          <button
            type="button"
            title="Open files panel"
            onClick={() => setIsFilesOpen(true)}
            className="fixed right-6 top-6 w-12 h-12 bg-white border border-gray-200 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center group"
          >
            <FileText className="h-5 w-5 text-gray-600 group-hover:text-blue-600" />
          </button>
        )}

        {/* New Chat Dialog */}
        <NewChatDialog
          open={isNewChatOpen}
          onOpenChange={setIsNewChatOpen}
          onChatCreated={handleChatCreated}
        />
      </div>
    </DashboardLayout>
  )
}
