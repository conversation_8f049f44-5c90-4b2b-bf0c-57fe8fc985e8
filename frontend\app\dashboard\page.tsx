'use client'

import { useEffect, useState } from 'react'
import { useAuthStore } from '@/lib/stores/auth-store'
import { useWebSocketStore } from '@/lib/stores/websocket-store'
import { useRouter } from 'next/navigation'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { ChatInterface } from '@/components/chat/chat-interface'
import { ChatHistory } from '@/components/chat/chat-history'
import { FileViewer } from '@/components/files/file-viewer'
import { NewChatDialog } from '@/components/chat/new-chat-dialog'
import { Button } from '@/components/ui/button'
import { Plus, Menu, X, FileText, Bot, Search } from 'lucide-react'
import { cn } from '@/lib/utils'

export default function DashboardPage() {
  const { user, isAuthenticated } = useAuthStore()
  const { connect } = useWebSocketStore()
  const router = useRouter()

  const [selectedChatId, setSelectedChatId] = useState<string | null>(null)
  const [isHistoryOpen, setIsHistoryOpen] = useState(true)
  const [isFilesOpen, setIsFilesOpen] = useState(false)
  const [isNewChatOpen, setIsNewChatOpen] = useState(false)

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login')
      return
    }

    // Connect to WebSocket
    if (user?.id) {
      connect(user.id)
    }
  }, [isAuthenticated, user, router, connect])

  const handleNewChat = () => {
    setIsNewChatOpen(true)
  }

  const handleChatCreated = (chatId: string) => {
    setSelectedChatId(chatId)
    setIsNewChatOpen(false)
  }

  const handleChatSelect = (chatId: string) => {
    setSelectedChatId(chatId)
  }

  if (!isAuthenticated || !user) {
    return null
  }

  return (
    <DashboardLayout>
      <div className="h-screen bg-white flex">
        {/* Left Sidebar - ChatGPT Style */}
        <div className="w-64 bg-gray-900 text-white flex flex-col">
          {/* Header */}
          <div className="p-4">
            <button
              type="button"
              onClick={handleNewChat}
              className="w-full flex items-center gap-3 px-3 py-2 text-sm border border-gray-600 rounded-lg hover:bg-gray-800 transition-colors"
            >
              <Plus className="h-4 w-4" />
              New chat
            </button>
          </div>

          {/* Search */}
          <div className="px-4 pb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search chats"
                className="w-full bg-gray-800 border border-gray-600 rounded-lg pl-10 pr-4 py-2 text-sm text-white placeholder-gray-400 focus:outline-none focus:border-gray-500"
              />
            </div>
          </div>

          {/* Chat History */}
          <div className="flex-1 overflow-y-auto px-2">
            <ChatHistory
              selectedChatId={selectedChatId}
              onChatSelect={handleChatSelect}
            />
          </div>

          {/* Bottom Section */}
          <div className="p-4 border-t border-gray-700">
            <div className="flex items-center gap-3 text-sm text-gray-300">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <Bot className="h-4 w-4 text-white" />
              </div>
              <div>
                <div className="font-medium">CrewAI</div>
                <div className="text-xs text-gray-400">AI Research Platform</div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col">
          {/* Top Bar */}
          <div className="h-14 border-b border-gray-200 px-6 flex items-center justify-between bg-white">
            <div className="flex items-center gap-3">
              <h1 className="font-semibold text-gray-900">
                {selectedChatId ? 'Research Session' : 'CrewAI Research'}
              </h1>
            </div>

            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2 px-3 py-1 bg-green-50 text-green-700 rounded-full text-xs font-medium">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                Online
              </div>
            </div>
          </div>

          {/* Chat Interface */}
          <div className="flex-1 overflow-hidden">
            <ChatInterface
              chatId={selectedChatId}
              onNewChat={handleNewChat}
            />
          </div>
        </div>

        {/* Right Sidebar - Files (Optional, can be toggled) */}
        <div className="w-80 bg-gray-50 border-l border-gray-200 flex flex-col">
          <div className="p-4 border-b border-gray-200">
            <h2 className="font-semibold text-gray-900 mb-3">Research Files</h2>
            <div className="grid grid-cols-2 gap-2">
              <button type="button" className="p-2 bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors text-center">
                <FileText className="h-4 w-4 mx-auto mb-1 text-gray-600" />
                <span className="text-xs text-gray-700">Files</span>
              </button>
              <button type="button" className="p-2 bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors text-center">
                <Bot className="h-4 w-4 mx-auto mb-1 text-gray-600" />
                <span className="text-xs text-gray-700">Agents</span>
              </button>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto">
            <FileViewer chatId={selectedChatId} />
          </div>
        </div>

        {/* New Chat Dialog */}
        <NewChatDialog
          open={isNewChatOpen}
          onOpenChange={setIsNewChatOpen}
          onChatCreated={handleChatCreated}
        />
      </div>
    </DashboardLayout>
  )
}
