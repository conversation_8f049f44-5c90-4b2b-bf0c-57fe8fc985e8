'use client'

import { useEffect, useState } from 'react'
import { useAuthStore } from '@/lib/stores/auth-store'
import { useWebSocketStore } from '@/lib/stores/websocket-store'
import { useRouter } from 'next/navigation'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { ChatInterface } from '@/components/chat/chat-interface'
import { ChatHistory } from '@/components/chat/chat-history'
import { FileViewer } from '@/components/files/file-viewer'
import { NewChatDialog } from '@/components/chat/new-chat-dialog'
import { Button } from '@/components/ui/button'
import { Plus, Menu, X, FileText, Bot } from 'lucide-react'
import { cn } from '@/lib/utils'

export default function DashboardPage() {
  const { user, isAuthenticated } = useAuthStore()
  const { connect } = useWebSocketStore()
  const router = useRouter()

  const [selectedChatId, setSelectedChatId] = useState<string | null>(null)
  const [isHistoryOpen, setIsHistoryOpen] = useState(true)
  const [isFilesOpen, setIsFilesOpen] = useState(false)
  const [isNewChatOpen, setIsNewChatOpen] = useState(false)

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login')
      return
    }

    // Connect to WebSocket
    if (user?.id) {
      connect(user.id)
    }
  }, [isAuthenticated, user, router, connect])

  const handleNewChat = () => {
    setIsNewChatOpen(true)
  }

  const handleChatCreated = (chatId: string) => {
    setSelectedChatId(chatId)
    setIsNewChatOpen(false)
  }

  const handleChatSelect = (chatId: string) => {
    setSelectedChatId(chatId)
  }

  if (!isAuthenticated || !user) {
    return null
  }

  return (
    <DashboardLayout>
      <div className="flex h-screen relative overflow-hidden" style={{ background: 'var(--gradient-hero)' }}>
        {/* Beautiful Light Background */}
        <div className="absolute inset-0">
          <div className="light-orb"></div>
          <div className="light-orb"></div>
          <div className="light-orb"></div>

          <div className="decorative-shapes">
            <div className="shape shape-1"></div>
            <div className="shape shape-2"></div>
            <div className="shape shape-3"></div>
          </div>
        </div>

        {/* Left Sidebar - Chat History */}
        <div className={cn(
          "relative z-10 transition-all duration-500 ease-in-out",
          isHistoryOpen ? "w-80" : "w-0"
        )}>
          <div className={cn(
            "h-full flex flex-col modern-card border-r border-neutral-200 rounded-none",
            !isHistoryOpen && "hidden"
          )}>
            <div className="p-8 border-b border-neutral-200">
              <div className="flex items-center justify-between mb-8">
                <h2 className="font-bold text-3xl text-neutral-800">Chat History</h2>
                <button
                  type="button"
                  onClick={() => setIsHistoryOpen(false)}
                  className="p-3 rounded-xl hover:bg-neutral-100 text-neutral-500 hover:text-neutral-800 transition-colors"
                  title="Close chat history"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
              <button
                type="button"
                onClick={handleNewChat}
                className="modern-button w-full h-16 text-lg font-bold"
              >
                <Plus className="h-6 w-6 mr-3" />
                New Research
              </button>
            </div>
            <div className="flex-1 overflow-hidden">
              <ChatHistory
                selectedChatId={selectedChatId}
                onChatSelect={handleChatSelect}
              />
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col relative z-10">
          {/* Top Bar */}
          <div className="h-28 modern-card border-b border-neutral-200 px-12 flex items-center justify-between rounded-none">
            <div className="flex items-center gap-8">
              {!isHistoryOpen && (
                <button
                  type="button"
                  onClick={() => setIsHistoryOpen(true)}
                  className="p-4 rounded-xl hover:bg-neutral-100 text-neutral-500 hover:text-neutral-800 transition-colors"
                  title="Open chat history"
                >
                  <Menu className="h-7 w-7" />
                </button>
              )}
              <div className="flex items-center gap-8">
                <div className="relative animate-bounce-gentle">
                  <div className="w-20 h-20 rounded-2xl flex items-center justify-center shadow-2xl modern-card">
                    <div className="w-16 h-16 rounded-2xl flex items-center justify-center" style={{ background: 'var(--gradient-primary)' }}>
                      <Bot className="h-8 w-8 text-white" />
                    </div>
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 rounded-full border-3 border-white animate-pulse modern-card" style={{ background: 'var(--primary-400)' }}></div>
                </div>
                <div>
                  <h1 className="font-black text-4xl text-neutral-800 gradient-text">
                    {selectedChatId ? 'Research Session' : 'Gemini CrewAI'}
                  </h1>
                  <p className="text-neutral-600 text-xl font-medium">Next-Generation AI Platform</p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <button
                type="button"
                onClick={() => setIsFilesOpen(!isFilesOpen)}
                className={cn(
                  "p-4 rounded-xl hover:bg-neutral-100 text-neutral-500 hover:text-neutral-800 transition-all duration-200",
                  isFilesOpen && "bg-blue-100 text-blue-600"
                )}
                title="Toggle file viewer"
              >
                <FileText className="h-7 w-7" />
              </button>
            </div>
          </div>

          {/* Chat Interface */}
          <div className="flex-1 overflow-hidden">
            <ChatInterface
              chatId={selectedChatId}
              onNewChat={handleNewChat}
            />
          </div>
        </div>

        {/* Right Sidebar - File Viewer */}
        <div className={cn(
          "relative z-10 transition-all duration-500 ease-in-out",
          isFilesOpen ? "w-96" : "w-0"
        )}>
          <div className={cn(
            "h-full flex flex-col modern-card border-l border-neutral-200 rounded-none",
            !isFilesOpen && "hidden"
          )}>
            <div className="p-8 border-b border-neutral-200">
              <div className="flex items-center justify-between">
                <h2 className="font-bold text-3xl text-neutral-800">Generated Files</h2>
                <button
                  type="button"
                  onClick={() => setIsFilesOpen(false)}
                  className="p-3 rounded-xl hover:bg-neutral-100 text-neutral-500 hover:text-neutral-800 transition-colors"
                  title="Close file viewer"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
            </div>
            <div className="flex-1 overflow-hidden">
              <FileViewer chatId={selectedChatId} />
            </div>
          </div>
        </div>
      </div>

      {/* New Chat Dialog */}
      <NewChatDialog
        open={isNewChatOpen}
        onOpenChange={setIsNewChatOpen}
        onChatCreated={handleChatCreated}
      />
    </DashboardLayout>
  )
}
