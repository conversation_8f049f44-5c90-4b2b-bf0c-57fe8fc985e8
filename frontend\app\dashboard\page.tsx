'use client'

import { useEffect, useState } from 'react'
import { useAuthStore } from '@/lib/stores/auth-store'
import { useWebSocketStore } from '@/lib/stores/websocket-store'
import { useRouter } from 'next/navigation'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { ChatInterface } from '@/components/chat/chat-interface'
import { ChatHistory } from '@/components/chat/chat-history'
import { FileViewer } from '@/components/files/file-viewer'
import { NewChatDialog } from '@/components/chat/new-chat-dialog'
import { Button } from '@/components/ui/button'
import { Plus, Menu, X, FileText, Bot, Search, MessageSquare } from 'lucide-react'
import { cn } from '@/lib/utils'

export default function DashboardPage() {
  const { user, isAuthenticated } = useAuthStore()
  const { connect } = useWebSocketStore()
  const router = useRouter()

  const [selectedChatId, setSelectedChatId] = useState<string | null>(null)
  const [isHistoryOpen, setIsHistoryOpen] = useState(true)
  const [isFilesOpen, setIsFilesOpen] = useState(false)
  const [isNewChatOpen, setIsNewChatOpen] = useState(false)

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login')
      return
    }

    // Connect to WebSocket
    if (user?.id) {
      connect(user.id)
    }
  }, [isAuthenticated, user, router, connect])

  const handleNewChat = () => {
    setIsNewChatOpen(true)
  }

  const handleChatCreated = (chatId: string) => {
    setSelectedChatId(chatId)
    setIsNewChatOpen(false)
  }

  const handleChatSelect = (chatId: string) => {
    setSelectedChatId(chatId)
  }

  if (!isAuthenticated || !user) {
    return null
  }

  return (
    <DashboardLayout>
      <div className="h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 flex">
        {/* Left Sidebar - Chat History */}
        <div className="w-80 bg-white/80 backdrop-blur-xl border-r border-indigo-100 shadow-xl flex flex-col">
          {/* Header with Menu */}
          <div className="p-6 bg-gradient-to-r from-indigo-600 to-purple-600 text-white">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg">
                  <Bot className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h1 className="font-bold text-lg">CrewAI</h1>
                  <p className="text-xs text-indigo-100">Research Platform</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <button type="button" title="Search" className="p-2 hover:bg-white/10 rounded-lg transition-colors">
                  <Search className="h-4 w-4" />
                </button>
                <button type="button" title="AI Settings" className="p-2 hover:bg-white/10 rounded-lg transition-colors">
                  <Bot className="h-4 w-4" />
                </button>
              </div>
            </div>

            <button
              type="button"
              onClick={handleNewChat}
              className="w-full bg-white/20 backdrop-blur-sm text-white rounded-xl py-3 px-4 font-semibold text-sm hover:bg-white/30 transition-all duration-200 shadow-lg flex items-center justify-center gap-2 border border-white/20"
            >
              <Plus className="h-4 w-4" />
              New Research Session
            </button>
          </div>

          {/* Navigation Menu */}
          <div className="p-4 border-b border-gray-100">
            <nav className="space-y-1">
              <button type="button" className="w-full flex items-center gap-3 px-3 py-2 text-sm font-medium text-indigo-600 bg-indigo-50 rounded-lg">
                <MessageSquare className="h-4 w-4" />
                Conversations
              </button>
              <button type="button" className="w-full flex items-center gap-3 px-3 py-2 text-sm font-medium text-gray-600 hover:text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors">
                <Bot className="h-4 w-4" />
                AI Agents
              </button>
              <button type="button" className="w-full flex items-center gap-3 px-3 py-2 text-sm font-medium text-gray-600 hover:text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors">
                <FileText className="h-4 w-4" />
                Templates
              </button>
            </nav>
          </div>

          {/* Chat History */}
          <div className="flex-1 overflow-y-auto px-4 py-2">
            <ChatHistory
              selectedChatId={selectedChatId}
              onChatSelect={handleChatSelect}
            />
          </div>

          {/* Bottom Section */}
          <div className="p-4 border-t border-gray-100 bg-gray-50/50">
            <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl border border-emerald-200">
              <div className="w-8 h-8 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-lg flex items-center justify-center shadow-sm">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
              <div className="flex-1">
                <div className="text-sm font-medium text-emerald-800">System Online</div>
                <div className="text-xs text-emerald-600">All agents ready</div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area - Chat Interface */}
        <div className="flex-1 flex flex-col bg-white/50 backdrop-blur-sm">
          {/* Top Navigation Bar */}
          <div className="h-16 bg-white/80 backdrop-blur-xl border-b border-gray-100 shadow-sm px-6 flex items-center justify-between">
            <div className="flex items-center gap-4">
              <h2 className="font-semibold text-gray-900">
                {selectedChatId ? 'Research Session' : 'Welcome to CrewAI'}
              </h2>
              {selectedChatId && (
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-sm text-gray-500">Active</span>
                </div>
              )}
            </div>

            <div className="flex items-center gap-3">
              <button type="button" title="Search" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
                <Search className="h-4 w-4 text-gray-600" />
              </button>
              <button type="button" title="AI Assistant" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
                <Bot className="h-4 w-4 text-gray-600" />
              </button>
              <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
                <span className="text-xs font-bold text-white">AI</span>
              </div>
            </div>
          </div>

          <ChatInterface
            chatId={selectedChatId}
            onNewChat={handleNewChat}
          />
        </div>

        {/* Right Sidebar - Files */}
        <div className="w-80 bg-white/80 backdrop-blur-xl border-l border-purple-100 shadow-xl flex flex-col">
          <div className="p-6 bg-gradient-to-r from-purple-600 to-pink-600 text-white">
            <div className="flex items-center justify-between mb-4">
              <h2 className="font-bold text-lg">Research Files</h2>
              <button
                type="button"
                title="Toggle files panel"
                onClick={() => setIsFilesOpen(!isFilesOpen)}
                className="p-2 hover:bg-white/10 rounded-lg transition-colors"
              >
                {isFilesOpen ? (
                  <X className="h-4 w-4" />
                ) : (
                  <FileText className="h-4 w-4" />
                )}
              </button>
            </div>

            {isFilesOpen && (
              <div className="grid grid-cols-2 gap-3">
                <button type="button" className="p-3 bg-white/20 backdrop-blur-sm hover:bg-white/30 rounded-xl transition-colors text-center border border-white/20">
                  <FileText className="h-5 w-5 mx-auto mb-2 text-white" />
                  <span className="text-xs font-medium">Documents</span>
                </button>
                <button type="button" className="p-3 bg-white/20 backdrop-blur-sm hover:bg-white/30 rounded-xl transition-colors text-center border border-white/20">
                  <Bot className="h-5 w-5 mx-auto mb-2 text-white" />
                  <span className="text-xs font-medium">Agents</span>
                </button>
              </div>
            )}
          </div>

          {/* File Categories */}
          <div className="p-4 border-b border-gray-100">
            <div className="space-y-2">
              <button type="button" className="w-full flex items-center gap-3 px-3 py-2 text-sm font-medium text-purple-600 bg-purple-50 rounded-lg">
                <FileText className="h-4 w-4" />
                Recent Files
              </button>
              <button type="button" className="w-full flex items-center gap-3 px-3 py-2 text-sm font-medium text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-colors">
                <Bot className="h-4 w-4" />
                AI Outputs
              </button>
              <button type="button" className="w-full flex items-center gap-3 px-3 py-2 text-sm font-medium text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-colors">
                <Search className="h-4 w-4" />
                Research Data
              </button>
            </div>
          </div>

          {isFilesOpen && (
            <div className="flex-1 overflow-y-auto p-4">
              <FileViewer chatId={selectedChatId} />
            </div>
          )}

          {!isFilesOpen && (
            <div className="flex-1 flex items-center justify-center p-4">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-100 to-pink-100 rounded-2xl flex items-center justify-center mx-auto mb-3">
                  <FileText className="h-8 w-8 text-purple-500" />
                </div>
                <p className="text-sm font-medium text-gray-700 mb-1">Files Panel</p>
                <p className="text-xs text-gray-500 mb-3">Manage your research files</p>
                <button
                  type="button"
                  onClick={() => setIsFilesOpen(true)}
                  className="text-xs text-purple-600 hover:text-purple-700 font-medium"
                >
                  Expand Panel
                </button>
              </div>
            </div>
          )}
        </div>

        {/* New Chat Dialog */}
        <NewChatDialog
          open={isNewChatOpen}
          onOpenChange={setIsNewChatOpen}
          onChatCreated={handleChatCreated}
        />
      </div>
    </DashboardLayout>
  )
}
