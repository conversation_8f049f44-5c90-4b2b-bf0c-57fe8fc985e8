'use client'

import { useEffect, useState } from 'react'
import { useAuthStore } from '@/lib/stores/auth-store'
import { useWebSocketStore } from '@/lib/stores/websocket-store'
import { useRouter } from 'next/navigation'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { ChatInterface } from '@/components/chat/chat-interface'
import { ChatHistory } from '@/components/chat/chat-history'
import { FileViewer } from '@/components/files/file-viewer'
import { NewChatDialog } from '@/components/chat/new-chat-dialog'
import { Button } from '@/components/ui/button'
import { Plus, Menu, X, FileText, Bot, Search, MessageSquare } from 'lucide-react'
import { cn } from '@/lib/utils'

export default function DashboardPage() {
  const { user, isAuthenticated } = useAuthStore()
  const { connect } = useWebSocketStore()
  const router = useRouter()

  const [selectedChatId, setSelectedChatId] = useState<string | null>(null)
  const [isHistoryOpen, setIsHistoryOpen] = useState(true)
  const [isFilesOpen, setIsFilesOpen] = useState(false)
  const [isNewChatOpen, setIsNewChatOpen] = useState(false)

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login')
      return
    }

    // Connect to WebSocket
    if (user?.id) {
      connect(user.id)
    }
  }, [isAuthenticated, user, router, connect])

  const handleNewChat = () => {
    setIsNewChatOpen(true)
  }

  const handleChatCreated = (chatId: string) => {
    setSelectedChatId(chatId)
    setIsNewChatOpen(false)
  }

  const handleChatDeleted = (chatId: string) => {
    if (selectedChatId === chatId) {
      setSelectedChatId(null)
    }
  }

  const handleChatSelect = (chatId: string) => {
    setSelectedChatId(chatId)
  }

  if (!isAuthenticated || !user) {
    return null
  }

  return (
    <DashboardLayout>
      <div className="animated-bg"></div>
      <div className="h-screen text-white flex overflow-hidden">
        {/* Left Sidebar - Ultra Modern 2025 */}
        <div className="w-72 sidebar flex flex-col relative">
          {/* Subtle gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-b from-blue-500/5 via-transparent to-purple-500/5 pointer-events-none" />

          {/* Header */}
          <div className="p-6 border-b border-[#1a1a1a] relative z-10">
            <div className="flex items-center gap-3 mb-6">
              <div className="relative floating">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/20 pulse-glow">
                  <Bot className="h-4 w-4 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-[#111111] animate-pulse" />
              </div>
              <div>
                <h1 className="font-bold text-lg gradient-text">CrewAI</h1>
                <p className="text-xs text-gray-500">Research Platform</p>
              </div>
            </div>

            <button
              type="button"
              onClick={handleNewChat}
              className="btn-primary w-full flex items-center justify-center gap-2"
            >
              <Plus className="h-4 w-4 transition-transform group-hover:rotate-90 duration-300" />
              New Research
            </button>
          </div>

          {/* Quick Actions */}
          <div className="p-4 border-b border-[#1a1a1a]">
            <div className="grid grid-cols-3 gap-2">
              <button type="button" className="p-3 bg-[#1a1a1a] hover:bg-[#222222] rounded-lg transition-all duration-200 group">
                <Search className="h-4 w-4 text-gray-400 group-hover:text-blue-400 mx-auto transition-colors" />
                <span className="text-xs text-gray-500 group-hover:text-gray-400 mt-1 block">Search</span>
              </button>
              <button type="button" className="p-3 bg-[#1a1a1a] hover:bg-[#222222] rounded-lg transition-all duration-200 group">
                <Bot className="h-4 w-4 text-gray-400 group-hover:text-purple-400 mx-auto transition-colors" />
                <span className="text-xs text-gray-500 group-hover:text-gray-400 mt-1 block">Agents</span>
              </button>
              <button type="button" className="p-3 bg-[#1a1a1a] hover:bg-[#222222] rounded-lg transition-all duration-200 group">
                <FileText className="h-4 w-4 text-gray-400 group-hover:text-green-400 mx-auto transition-colors" />
                <span className="text-xs text-gray-500 group-hover:text-gray-400 mt-1 block">Files</span>
              </button>
            </div>
          </div>

          {/* Chat History */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="mb-4">
              <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-3">Recent Conversations</h3>
            </div>
            <ChatHistory
              selectedChatId={selectedChatId}
              onChatSelect={handleChatSelect}
              onChatDeleted={handleChatDeleted}
            />
          </div>

          {/* Bottom Status */}
          <div className="p-4 border-t border-[#1a1a1a]">
            <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-xl border border-green-500/20">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
              <div className="flex-1">
                <div className="text-sm font-medium text-green-400">All Systems Online</div>
                <div className="text-xs text-green-500/70">Ready for research</div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col glass-dark relative">
          {/* Ambient background effects */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/3 via-transparent to-purple-500/3 pointer-events-none" />
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl pointer-events-none floating" />
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl pointer-events-none floating-delayed" />

          <ChatInterface
            chatId={selectedChatId}
            onNewChat={handleNewChat}
          />
        </div>

        {/* Right Sidebar - Files */}
        <div className="w-72 sidebar flex flex-col relative">
          {/* Subtle gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-b from-purple-500/5 via-transparent to-pink-500/5 pointer-events-none" />

          <div className="p-6 border-b border-[#1a1a1a] relative z-10">
            <div className="flex items-center justify-between mb-4">
              <h2 className="font-bold text-lg gradient-text">Research Hub</h2>
              <button
                type="button"
                title="Toggle files panel"
                onClick={() => setIsFilesOpen(!isFilesOpen)}
                className="p-2 hover:bg-[#1a1a1a] rounded-lg transition-all duration-200 group"
              >
                {isFilesOpen ? (
                  <X className="h-4 w-4 text-gray-400 group-hover:text-red-400 transition-colors" />
                ) : (
                  <FileText className="h-4 w-4 text-gray-400 group-hover:text-blue-400 transition-colors" />
                )}
              </button>
            </div>

            {isFilesOpen && (
              <div className="grid grid-cols-2 gap-3">
                <button type="button" className="card p-4 text-center group">
                  <FileText className="h-5 w-5 mx-auto mb-2 text-blue-400 group-hover:scale-110 transition-transform floating" />
                  <span className="text-xs font-medium text-blue-300">Documents</span>
                </button>
                <button type="button" className="card p-4 text-center group">
                  <Bot className="h-5 w-5 mx-auto mb-2 text-purple-400 group-hover:scale-110 transition-transform floating-delayed" />
                  <span className="text-xs font-medium text-purple-300">AI Outputs</span>
                </button>
              </div>
            )}
          </div>

          {isFilesOpen && (
            <div className="flex-1 overflow-y-auto p-4">
              <FileViewer chatId={selectedChatId} />
            </div>
          )}

          {!isFilesOpen && (
            <div className="flex-1 flex items-center justify-center p-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl flex items-center justify-center mx-auto mb-4 border border-gray-700">
                  <FileText className="h-8 w-8 text-gray-500" />
                </div>
                <p className="text-sm font-medium text-gray-400 mb-1">Research Hub</p>
                <p className="text-xs text-gray-600 mb-4">Access your files and AI outputs</p>
                <button
                  type="button"
                  onClick={() => setIsFilesOpen(true)}
                  className="text-xs text-blue-400 hover:text-blue-300 font-medium transition-colors"
                >
                  Expand Panel
                </button>
              </div>
            </div>
          )}
        </div>

        {/* New Chat Dialog */}
        <NewChatDialog
          open={isNewChatOpen}
          onOpenChange={setIsNewChatOpen}
          onChatCreated={handleChatCreated}
        />
      </div>
    </DashboardLayout>
  )
}
