'use client'

import { useEffect, useState } from 'react'
import { useAuthStore } from '@/lib/stores/auth-store'
import { useWebSocketStore } from '@/lib/stores/websocket-store'
import { useRouter } from 'next/navigation'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { ChatInterface } from '@/components/chat/chat-interface'
import { ChatHistory } from '@/components/chat/chat-history'
import { FileViewer } from '@/components/files/file-viewer'
import { NewChatDialog } from '@/components/chat/new-chat-dialog'
import { Button } from '@/components/ui/button'
import { Plus, Menu, X, FileText } from 'lucide-react'
import { cn } from '@/lib/utils'

export default function DashboardPage() {
  const { user, isAuthenticated } = useAuthStore()
  const { connect } = useWebSocketStore()
  const router = useRouter()
  
  const [selectedChatId, setSelectedChatId] = useState<string | null>(null)
  const [isHistoryOpen, setIsHistoryOpen] = useState(true)
  const [isFilesOpen, setIsFilesOpen] = useState(false)
  const [isNewChatOpen, setIsNewChatOpen] = useState(false)

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login')
      return
    }

    // Connect to WebSocket
    if (user?.id) {
      connect(user.id)
    }
  }, [isAuthenticated, user, router, connect])

  const handleNewChat = () => {
    setIsNewChatOpen(true)
  }

  const handleChatCreated = (chatId: string) => {
    setSelectedChatId(chatId)
    setIsNewChatOpen(false)
  }

  const handleChatSelect = (chatId: string) => {
    setSelectedChatId(chatId)
  }

  if (!isAuthenticated || !user) {
    return null
  }

  return (
    <DashboardLayout>
      <div className="flex h-screen bg-background">
        {/* Left Sidebar - Chat History */}
        <div className={cn(
          "transition-all duration-300 ease-in-out border-r bg-card",
          isHistoryOpen ? "w-80" : "w-0"
        )}>
          <div className={cn(
            "h-full flex flex-col",
            !isHistoryOpen && "hidden"
          )}>
            <div className="p-4 border-b">
              <div className="flex items-center justify-between mb-4">
                <h2 className="font-semibold text-lg">Chat History</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsHistoryOpen(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <Button 
                onClick={handleNewChat}
                className="w-full"
                size="sm"
              >
                <Plus className="h-4 w-4 mr-2" />
                New Research
              </Button>
            </div>
            <div className="flex-1 overflow-hidden">
              <ChatHistory 
                selectedChatId={selectedChatId}
                onChatSelect={handleChatSelect}
              />
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col">
          {/* Top Bar */}
          <div className="h-14 border-b bg-card px-4 flex items-center justify-between">
            <div className="flex items-center gap-2">
              {!isHistoryOpen && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsHistoryOpen(true)}
                >
                  <Menu className="h-4 w-4" />
                </Button>
              )}
              <h1 className="font-semibold">
                {selectedChatId ? 'Research Session' : 'Gemini CrewAI'}
              </h1>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsFilesOpen(!isFilesOpen)}
                className={cn(isFilesOpen && "bg-accent")}
              >
                <FileText className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Chat Interface */}
          <div className="flex-1 overflow-hidden">
            <ChatInterface 
              chatId={selectedChatId}
              onNewChat={handleNewChat}
            />
          </div>
        </div>

        {/* Right Sidebar - File Viewer */}
        <div className={cn(
          "transition-all duration-300 ease-in-out border-l bg-card",
          isFilesOpen ? "w-96" : "w-0"
        )}>
          <div className={cn(
            "h-full flex flex-col",
            !isFilesOpen && "hidden"
          )}>
            <div className="p-4 border-b">
              <div className="flex items-center justify-between">
                <h2 className="font-semibold text-lg">Generated Files</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsFilesOpen(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="flex-1 overflow-hidden">
              <FileViewer chatId={selectedChatId} />
            </div>
          </div>
        </div>
      </div>

      {/* New Chat Dialog */}
      <NewChatDialog
        open={isNewChatOpen}
        onOpenChange={setIsNewChatOpen}
        onChatCreated={handleChatCreated}
      />
    </DashboardLayout>
  )
}
