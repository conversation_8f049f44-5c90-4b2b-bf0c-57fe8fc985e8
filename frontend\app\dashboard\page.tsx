'use client'

import { useEffect, useState } from 'react'
import { useAuthStore } from '@/lib/stores/auth-store'
import { useWebSocketStore } from '@/lib/stores/websocket-store'
import { useRouter } from 'next/navigation'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { ChatInterface } from '@/components/chat/chat-interface'
import { ChatHistory } from '@/components/chat/chat-history'
import { FileViewer } from '@/components/files/file-viewer'
import { NewChatDialog } from '@/components/chat/new-chat-dialog'
import { Button } from '@/components/ui/button'
import { Plus, Menu, X, FileText, Bot } from 'lucide-react'
import { cn } from '@/lib/utils'

export default function DashboardPage() {
  const { user, isAuthenticated } = useAuthStore()
  const { connect } = useWebSocketStore()
  const router = useRouter()

  const [selectedChatId, setSelectedChatId] = useState<string | null>(null)
  const [isHistoryOpen, setIsHistoryOpen] = useState(true)
  const [isFilesOpen, setIsFilesOpen] = useState(false)
  const [isNewChatOpen, setIsNewChatOpen] = useState(false)

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login')
      return
    }

    // Connect to WebSocket
    if (user?.id) {
      connect(user.id)
    }
  }, [isAuthenticated, user, router, connect])

  const handleNewChat = () => {
    setIsNewChatOpen(true)
  }

  const handleChatCreated = (chatId: string) => {
    setSelectedChatId(chatId)
    setIsNewChatOpen(false)
  }

  const handleChatSelect = (chatId: string) => {
    setSelectedChatId(chatId)
  }

  if (!isAuthenticated || !user) {
    return null
  }

  return (
    <DashboardLayout>
      <div className="h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        {/* Beautiful 3-Column Layout (3:6:3) */}
        <div className="grid grid-cols-12 h-full gap-0">

          {/* Left Column - Chat History (3/12 = 25%) */}
          <div className="col-span-3 h-full">
            <div className="h-full flex flex-col neo-card rounded-none border-r border-neutral-200">
              {/* Header */}
              <div className="p-6 border-b border-neutral-200 bg-gradient-to-r from-blue-50 to-purple-50">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                    <Bot className="h-5 w-5 text-white" />
                  </div>
                  <h2 className="font-bold text-xl text-neutral-900">Conversations</h2>
                </div>
                <button
                  type="button"
                  onClick={handleNewChat}
                  className="neo-button w-full h-12 text-sm font-bold"
                >
                  <Plus className="h-4 w-4" />
                  New Research
                </button>
              </div>

              {/* Chat List */}
              <div className="flex-1 overflow-y-auto">
                <ChatHistory
                  selectedChatId={selectedChatId}
                  onChatSelect={handleChatSelect}
                />
              </div>
            </div>
          </div>

          {/* Center Column - Main Chat (6/12 = 50%) */}
          <div className="col-span-6 h-full">
            <div className="h-full flex flex-col">
              {/* Top Bar */}
              <div className="h-16 neo-card border-b border-neutral-200 px-6 flex items-center justify-between rounded-none bg-white/80 backdrop-blur-sm">
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <div className="w-10 h-10 rounded-lg flex items-center justify-center bg-gradient-to-r from-blue-500 to-purple-600 shadow-md">
                      <Bot className="h-5 w-5 text-white" />
                    </div>
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white animate-pulse"></div>
                  </div>
                  <div>
                    <h1 className="font-bold text-lg text-neutral-900">
                      {selectedChatId ? 'Research Session' : 'Gemini CrewAI'}
                    </h1>
                    <p className="text-neutral-500 text-xs">AI-Powered Research Platform</p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-1 px-3 py-1 bg-green-100 rounded-full">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-xs font-medium text-green-700">Online</span>
                  </div>
                </div>
              </div>

              {/* Chat Interface */}
              <div className="flex-1 overflow-hidden bg-white">
                <ChatInterface
                  chatId={selectedChatId}
                  onNewChat={handleNewChat}
                />
              </div>
            </div>
          </div>

          {/* Right Column - Files & Tools (3/12 = 25%) */}
          <div className="col-span-3 h-full">
            <div className="h-full flex flex-col neo-card rounded-none border-l border-neutral-200">
              {/* Header */}
              <div className="p-6 border-b border-neutral-200 bg-gradient-to-r from-purple-50 to-pink-50">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center">
                    <FileText className="h-5 w-5 text-white" />
                  </div>
                  <h2 className="font-bold text-xl text-neutral-900">Research Files</h2>
                </div>

                {/* Quick Actions */}
                <div className="grid grid-cols-2 gap-2">
                  <button
                    type="button"
                    className="p-3 bg-white rounded-lg border border-neutral-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 text-center"
                  >
                    <FileText className="h-4 w-4 mx-auto mb-1 text-neutral-600" />
                    <span className="text-xs font-medium text-neutral-700">Documents</span>
                  </button>
                  <button
                    type="button"
                    className="p-3 bg-white rounded-lg border border-neutral-200 hover:border-purple-300 hover:bg-purple-50 transition-all duration-200 text-center"
                  >
                    <Bot className="h-4 w-4 mx-auto mb-1 text-neutral-600" />
                    <span className="text-xs font-medium text-neutral-700">Agents</span>
                  </button>
                </div>
              </div>

              {/* File Viewer */}
              <div className="flex-1 overflow-y-auto">
                <FileViewer chatId={selectedChatId} />
              </div>
            </div>
          </div>
        </div>

        {/* New Chat Dialog */}
        <NewChatDialog
          open={isNewChatOpen}
          onOpenChange={setIsNewChatOpen}
          onChatCreated={handleChatCreated}
        />
      </div>
    </DashboardLayout>
  )
}
