'use client'

import { useEffect, useState } from 'react'
import { useAuthStore } from '@/lib/stores/auth-store'
import { useWebSocketStore } from '@/lib/stores/websocket-store'
import { useRouter } from 'next/navigation'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { ChatInterface } from '@/components/chat/chat-interface'
import { ChatHistory } from '@/components/chat/chat-history'
import { FileViewer } from '@/components/files/file-viewer'
import { NewChatDialog } from '@/components/chat/new-chat-dialog'
import { Button } from '@/components/ui/button'
import { Plus, Menu, X, FileText, Bot, Search } from 'lucide-react'
import { cn } from '@/lib/utils'

export default function DashboardPage() {
  const { user, isAuthenticated } = useAuthStore()
  const { connect } = useWebSocketStore()
  const router = useRouter()

  const [selectedChatId, setSelectedChatId] = useState<string | null>(null)
  const [isHistoryOpen, setIsHistoryOpen] = useState(true)
  const [isFilesOpen, setIsFilesOpen] = useState(false)
  const [isNewChatOpen, setIsNewChatOpen] = useState(false)

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login')
      return
    }

    // Connect to WebSocket
    if (user?.id) {
      connect(user.id)
    }
  }, [isAuthenticated, user, router, connect])

  const handleNewChat = () => {
    setIsNewChatOpen(true)
  }

  const handleChatCreated = (chatId: string) => {
    setSelectedChatId(chatId)
    setIsNewChatOpen(false)
  }

  const handleChatSelect = (chatId: string) => {
    setSelectedChatId(chatId)
  }

  if (!isAuthenticated || !user) {
    return null
  }

  return (
    <DashboardLayout>
      <div className="h-screen bg-black text-white flex">
        {/* Left Sidebar - Chat History */}
        <div className="w-80 bg-zinc-900/50 backdrop-blur-xl border-r border-zinc-800/50 flex flex-col">
          {/* Header */}
          <div className="p-6">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                <Bot className="h-4 w-4 text-black" />
              </div>
              <span className="font-medium text-white">CrewAI</span>
            </div>

            <button
              type="button"
              onClick={handleNewChat}
              className="w-full bg-white text-black rounded-lg py-2.5 px-4 font-medium text-sm hover:bg-gray-100 transition-colors flex items-center justify-center gap-2"
            >
              <Plus className="h-4 w-4" />
              New chat
            </button>
          </div>

          {/* Chat History */}
          <div className="flex-1 overflow-y-auto px-3">
            <div className="mb-4">
              <h3 className="text-xs font-medium text-zinc-400 uppercase tracking-wider px-3 mb-2">
                Conversations
              </h3>
            </div>
            <ChatHistory
              selectedChatId={selectedChatId}
              onChatSelect={handleChatSelect}
            />
          </div>

          {/* Bottom Section */}
          <div className="p-4 border-t border-zinc-800/50">
            <div className="text-xs text-zinc-400">
              CrewAI Research Platform
            </div>
          </div>
        </div>

        {/* Main Content Area - Chat Interface */}
        <div className="flex-1 flex flex-col">
          <ChatInterface
            chatId={selectedChatId}
            onNewChat={handleNewChat}
          />
        </div>

        {/* Right Sidebar - Files */}
        <div className="w-80 bg-zinc-900/30 backdrop-blur-xl border-l border-zinc-800/50 flex flex-col">
          <div className="p-6 border-b border-zinc-800/50">
            <div className="flex items-center justify-between mb-4">
              <h2 className="font-medium text-white">Research Files</h2>
              <button
                type="button"
                title="Toggle files panel"
                onClick={() => setIsFilesOpen(!isFilesOpen)}
                className="p-1.5 hover:bg-zinc-800 rounded-md transition-colors"
              >
                {isFilesOpen ? (
                  <X className="h-4 w-4 text-zinc-400" />
                ) : (
                  <FileText className="h-4 w-4 text-zinc-400" />
                )}
              </button>
            </div>

            {isFilesOpen && (
              <div className="grid grid-cols-2 gap-2">
                <button type="button" className="p-2 bg-zinc-800/50 hover:bg-zinc-700/50 rounded-lg transition-colors text-center">
                  <FileText className="h-4 w-4 mx-auto mb-1 text-zinc-300" />
                  <span className="text-xs text-zinc-400">Docs</span>
                </button>
                <button type="button" className="p-2 bg-zinc-800/50 hover:bg-zinc-700/50 rounded-lg transition-colors text-center">
                  <Bot className="h-4 w-4 mx-auto mb-1 text-zinc-300" />
                  <span className="text-xs text-zinc-400">Agents</span>
                </button>
              </div>
            )}
          </div>

          {isFilesOpen && (
            <div className="flex-1 overflow-y-auto">
              <FileViewer chatId={selectedChatId} />
            </div>
          )}

          {!isFilesOpen && (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <FileText className="h-8 w-8 text-zinc-600 mx-auto mb-2" />
                <p className="text-sm text-zinc-500">Files panel collapsed</p>
                <button
                  type="button"
                  onClick={() => setIsFilesOpen(true)}
                  className="text-xs text-zinc-400 hover:text-zinc-300 mt-1"
                >
                  Click to expand
                </button>
              </div>
            </div>
          )}
        </div>

        {/* New Chat Dialog */}
        <NewChatDialog
          open={isNewChatOpen}
          onOpenChange={setIsNewChatOpen}
          onChatCreated={handleChatCreated}
        />
      </div>
    </DashboardLayout>
  )
}
