'use client'

import { useEffect, useState } from 'react'
import { useAuthStore } from '@/lib/stores/auth-store'
import { useWebSocketStore } from '@/lib/stores/websocket-store'
import { useRouter } from 'next/navigation'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { ChatInterface } from '@/components/chat/chat-interface'
import { ChatHistory } from '@/components/chat/chat-history'
import { FileViewer } from '@/components/files/file-viewer'
import { NewChatDialog } from '@/components/chat/new-chat-dialog'
import { Button } from '@/components/ui/button'
import { Plus, Menu, X, FileText, Bot } from 'lucide-react'
import { cn } from '@/lib/utils'

export default function DashboardPage() {
  const { user, isAuthenticated } = useAuthStore()
  const { connect } = useWebSocketStore()
  const router = useRouter()

  const [selectedChatId, setSelectedChatId] = useState<string | null>(null)
  const [isHistoryOpen, setIsHistoryOpen] = useState(true)
  const [isFilesOpen, setIsFilesOpen] = useState(false)
  const [isNewChatOpen, setIsNewChatOpen] = useState(false)

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login')
      return
    }

    // Connect to WebSocket
    if (user?.id) {
      connect(user.id)
    }
  }, [isAuthenticated, user, router, connect])

  const handleNewChat = () => {
    setIsNewChatOpen(true)
  }

  const handleChatCreated = (chatId: string) => {
    setSelectedChatId(chatId)
    setIsNewChatOpen(false)
  }

  const handleChatSelect = (chatId: string) => {
    setSelectedChatId(chatId)
  }

  if (!isAuthenticated || !user) {
    return null
  }

  return (
    <DashboardLayout>
      <div className="flex h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900 relative overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-[linear-gradient(to_right,#4f46e5_1px,transparent_1px),linear-gradient(to_bottom,#4f46e5_1px,transparent_1px)] bg-[size:4rem_4rem] opacity-10"></div>
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/20 to-purple-600/20 rounded-full filter blur-3xl"></div>
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400/20 to-pink-600/20 rounded-full filter blur-3xl"></div>
        </div>

        {/* Left Sidebar - Chat History */}
        <div className={cn(
          "relative z-10 transition-all duration-500 ease-in-out",
          isHistoryOpen ? "w-80" : "w-0"
        )}>
          <div className={cn(
            "h-full flex flex-col backdrop-blur-xl bg-white/5 border-r border-white/10",
            !isHistoryOpen && "hidden"
          )}>
            <div className="p-6 border-b border-white/10">
              <div className="flex items-center justify-between mb-6">
                <h2 className="font-bold text-2xl text-white">Chat History</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsHistoryOpen(false)}
                  className="hover:bg-white/10 text-white"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>
              <Button
                onClick={handleNewChat}
                className="w-full h-12 bg-gradient-to-r from-blue-500 via-purple-600 to-pink-600 hover:from-blue-600 hover:via-purple-700 hover:to-pink-700 text-white font-bold shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-[1.02] rounded-xl"
              >
                <Plus className="h-5 w-5 mr-2" />
                New Research
              </Button>
            </div>
            <div className="flex-1 overflow-hidden">
              <ChatHistory
                selectedChatId={selectedChatId}
                onChatSelect={handleChatSelect}
              />
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col relative z-10">
          {/* Top Bar */}
          <div className="h-20 backdrop-blur-xl bg-white/5 border-b border-white/10 px-8 flex items-center justify-between">
            <div className="flex items-center gap-4">
              {!isHistoryOpen && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsHistoryOpen(true)}
                  className="hover:bg-white/10 text-white p-3"
                >
                  <Menu className="h-5 w-5" />
                </Button>
              )}
              <div className="flex items-center gap-4">
                <div className="relative">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 via-purple-600 to-pink-600 rounded-2xl flex items-center justify-center shadow-2xl">
                    <Bot className="h-6 w-6 text-white" />
                  </div>
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white animate-pulse"></div>
                </div>
                <div>
                  <h1 className="font-black text-2xl text-white">
                    {selectedChatId ? 'Research Session' : 'Gemini CrewAI'}
                  </h1>
                  <p className="text-slate-300 text-sm">Next-Generation AI Platform</p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsFilesOpen(!isFilesOpen)}
                className={cn(
                  "hover:bg-white/10 text-white p-3 transition-all duration-200",
                  isFilesOpen && "bg-white/20 text-blue-300"
                )}
              >
                <FileText className="h-5 w-5" />
              </Button>
            </div>
          </div>

          {/* Chat Interface */}
          <div className="flex-1 overflow-hidden">
            <ChatInterface
              chatId={selectedChatId}
              onNewChat={handleNewChat}
            />
          </div>
        </div>

        {/* Right Sidebar - File Viewer */}
        <div className={cn(
          "relative z-10 transition-all duration-500 ease-in-out",
          isFilesOpen ? "w-96" : "w-0"
        )}>
          <div className={cn(
            "h-full flex flex-col backdrop-blur-xl bg-white/5 border-l border-white/10",
            !isFilesOpen && "hidden"
          )}>
            <div className="p-6 border-b border-white/10">
              <div className="flex items-center justify-between">
                <h2 className="font-bold text-2xl text-white">Generated Files</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsFilesOpen(false)}
                  className="hover:bg-white/10 text-white"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>
            </div>
            <div className="flex-1 overflow-hidden">
              <FileViewer chatId={selectedChatId} />
            </div>
          </div>
        </div>
      </div>

      {/* New Chat Dialog */}
      <NewChatDialog
        open={isNewChatOpen}
        onOpenChange={setIsNewChatOpen}
        onChatCreated={handleChatCreated}
      />
    </DashboardLayout>
  )
}
