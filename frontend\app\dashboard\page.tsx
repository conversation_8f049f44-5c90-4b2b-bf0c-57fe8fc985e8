'use client'

import { useEffect, useState } from 'react'
import { useAuthStore } from '@/lib/stores/auth-store'
import { useWebSocketStore } from '@/lib/stores/websocket-store'
import { useRouter } from 'next/navigation'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { ChatInterface } from '@/components/chat/chat-interface'
import { ChatHistory } from '@/components/chat/chat-history'
import { FileViewer } from '@/components/files/file-viewer'
import { NewChatDialog } from '@/components/chat/new-chat-dialog'
import { Button } from '@/components/ui/button'
import { Plus, Menu, X, FileText } from 'lucide-react'
import { cn } from '@/lib/utils'

export default function DashboardPage() {
  const { user, isAuthenticated } = useAuthStore()
  const { connect } = useWebSocketStore()
  const router = useRouter()

  const [selectedChatId, setSelectedChatId] = useState<string | null>(null)
  const [isHistoryOpen, setIsHistoryOpen] = useState(true)
  const [isFilesOpen, setIsFilesOpen] = useState(false)
  const [isNewChatOpen, setIsNewChatOpen] = useState(false)

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login')
      return
    }

    // Connect to WebSocket
    if (user?.id) {
      connect(user.id)
    }
  }, [isAuthenticated, user, router, connect])

  const handleNewChat = () => {
    setIsNewChatOpen(true)
  }

  const handleChatCreated = (chatId: string) => {
    setSelectedChatId(chatId)
    setIsNewChatOpen(false)
  }

  const handleChatSelect = (chatId: string) => {
    setSelectedChatId(chatId)
  }

  if (!isAuthenticated || !user) {
    return null
  }

  return (
    <DashboardLayout>
      <div className="flex h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        {/* Left Sidebar - Chat History */}
        <div className={cn(
          "transition-all duration-300 ease-in-out border-r border-slate-200 bg-white/80 backdrop-blur-sm shadow-lg",
          isHistoryOpen ? "w-80" : "w-0"
        )}>
          <div className={cn(
            "h-full flex flex-col",
            !isHistoryOpen && "hidden"
          )}>
            <div className="p-6 border-b border-slate-200">
              <div className="flex items-center justify-between mb-4">
                <h2 className="font-bold text-xl text-slate-800">Chat History</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsHistoryOpen(false)}
                  className="hover:bg-slate-100"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <Button
                onClick={handleNewChat}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                size="sm"
              >
                <Plus className="h-4 w-4 mr-2" />
                New Research
              </Button>
            </div>
            <div className="flex-1 overflow-hidden">
              <ChatHistory
                selectedChatId={selectedChatId}
                onChatSelect={handleChatSelect}
              />
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col">
          {/* Top Bar */}
          <div className="h-16 border-b border-slate-200 bg-white/80 backdrop-blur-sm px-6 flex items-center justify-between shadow-sm">
            <div className="flex items-center gap-3">
              {!isHistoryOpen && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsHistoryOpen(true)}
                  className="hover:bg-slate-100"
                >
                  <Menu className="h-4 w-4" />
                </Button>
              )}
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <Bot className="h-4 w-4 text-white" />
                </div>
                <h1 className="font-bold text-xl text-slate-800">
                  {selectedChatId ? 'Research Session' : 'Gemini CrewAI'}
                </h1>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsFilesOpen(!isFilesOpen)}
                className={cn(
                  "hover:bg-slate-100 transition-colors",
                  isFilesOpen && "bg-blue-100 text-blue-700"
                )}
              >
                <FileText className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Chat Interface */}
          <div className="flex-1 overflow-hidden bg-gradient-to-br from-slate-50 to-blue-50">
            <ChatInterface
              chatId={selectedChatId}
              onNewChat={handleNewChat}
            />
          </div>
        </div>

        {/* Right Sidebar - File Viewer */}
        <div className={cn(
          "transition-all duration-300 ease-in-out border-l border-slate-200 bg-white/80 backdrop-blur-sm shadow-lg",
          isFilesOpen ? "w-96" : "w-0"
        )}>
          <div className={cn(
            "h-full flex flex-col",
            !isFilesOpen && "hidden"
          )}>
            <div className="p-6 border-b border-slate-200">
              <div className="flex items-center justify-between">
                <h2 className="font-bold text-xl text-slate-800">Generated Files</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsFilesOpen(false)}
                  className="hover:bg-slate-100"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="flex-1 overflow-hidden">
              <FileViewer chatId={selectedChatId} />
            </div>
          </div>
        </div>
      </div>

      {/* New Chat Dialog */}
      <NewChatDialog
        open={isNewChatOpen}
        onOpenChange={setIsNewChatOpen}
        onChatCreated={handleChatCreated}
      />
    </DashboardLayout>
  )
}
