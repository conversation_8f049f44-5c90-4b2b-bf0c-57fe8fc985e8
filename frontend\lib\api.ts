import axios from 'axios'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

export const api = axios.create({
  baseURL: `${API_BASE_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    // Token will be set by auth store
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      // The auth store will handle logout
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// API functions
export const authAPI = {
  login: (email: string, password: string) =>
    api.post('/auth/login', { email, password }),
  
  getMe: () => api.get('/auth/me'),
  
  verifyToken: () => api.post('/auth/verify-token'),
}

export const chatAPI = {
  getHistory: (limit = 50, offset = 0) =>
    api.get(`/chat/history?limit=${limit}&offset=${offset}`),
  
  getChat: (chatId: string) => api.get(`/chat/${chatId}`),
  
  deleteChat: (chatId: string) => api.delete(`/chat/${chatId}`),
  
  getMessages: (chatId: string) => api.get(`/chat/${chatId}/messages`),
}

export const crewAPI = {
  execute: (topic: string) => api.post('/crew/execute', { topic }),
}

export const filesAPI = {
  getChatFiles: (chatId: string) => api.get(`/files/chat/${chatId}`),
  
  getFile: (fileId: string) => api.get(`/files/${fileId}`),
  
  downloadFile: (fileId: string) => api.get(`/files/${fileId}/download`),
  
  deleteFile: (fileId: string) => api.delete(`/files/${fileId}`),
}

export const adminAPI = {
  getUsers: (limit = 100, offset = 0) =>
    api.get(`/admin/users?limit=${limit}&offset=${offset}`),
  
  createUser: (userData: {
    email: string
    username: string
    password: string
    full_name?: string
    is_admin?: boolean
  }) => api.post('/admin/users', userData),
  
  updateUser: (userId: string, userData: any) =>
    api.put(`/admin/users/${userId}`, userData),
  
  deleteUser: (userId: string) => api.delete(`/admin/users/${userId}`),
}
